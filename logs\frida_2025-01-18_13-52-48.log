2025-01-18 13:52:48,571 - INFO - Connected to device: <PERSON><PERSON>(id="socket@**************:27042", name="**************:27042", type='remote')
2025-01-18 13:52:48,571 - INFO - Available processes:
2025-01-18 13:52:49,630 - INFO - Process(pid=32175, name="Gadget", parameters={})
2025-01-18 13:52:54,797 - INFO - <PERSON>ript loaded successfully
2025-01-18 13:52:58,100 - ERROR - Script error: {'type': 'error', 'description': 'Error: expected a pointer', 'stack': 'Error: expected a pointer\n    at sendEvent (/script1.js:2855)\n    at click (/script1.js:2832)\n    at handleConsoleCommand (/script1.js:14006)\n    at handleCommand (/script1.js:50437)\n    at <anonymous> (frida/runtime/message-dispatcher.js:56)\n    at i (frida/runtime/message-dispatcher.js:42)\n    at forEach (native)\n    at c (frida/runtime/message-dispatcher.js:32)\n    at o (frida/runtime/message-dispatcher.js:23)', 'fileName': '/script1.js', 'lineNumber': 2855, 'columnNumber': 1}
