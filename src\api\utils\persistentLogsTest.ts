import { log, logToPersistentFile, readPersistentLogs, getPersistentLogPath } from './utils'

/**
 * Test function to demonstrate the persistent logging system
 */
export function testPersistentLogs() {
    // Log the current path
    log(`Persistent log file path: ${getPersistentLogPath()}`)
    
    // Write a test log entry
    const success = logToPersistentFile('Test log entry from persistentLogsTest.ts')
    log(`Writing test log entry: ${success ? 'SUCCESS' : 'FAILED'}`)
    
    // Read all log entries
    const logs = readPersistentLogs()
    log(`Read ${logs.length} log entries:`)
    logs.forEach((entry, index) => {
        log(`  ${index + 1}. ${entry}`)
    })
}

/**
 * Example of how to use the persistent logging system in your code:
 * 
 * 1. Import the necessary functions:
 *    import { logToPersistentFile, readPersistentLogs } from '../api/utils/utils'
 * 
 * 2. Log important events:
 *    logToPersistentFile('<PERSON><PERSON><PERSON> started with parameters: ' + JSON.stringify(params))
 * 
 * 3. Log errors:
 *    try {
 *        // Some code that might fail
 *    } catch (e) {
 *        logToPersistentFile(`Error in myFunction: ${e.message}`)
 *    }
 * 
 * 4. Read logs when needed:
 *    const logs = readPersistentLogs()
 *    logs.forEach(entry => console.log(entry))
 */
