// import { deepFindClass } from "../api/utils/utils";


// export class FirebaseDocumentRef {
//   private docRef: any;
  
//   constructor(docRef: any) {
//     this.docRef = docRef;
//   }
  
//    public static create(collection: string, documentId: string): FirebaseDocumentRef {
//       // const FirestoreHelper = Java.use('com.szczawicznecrew.osrstools.FirestoreHelper');
//       const FirestoreHelper = deepFindClass('com.szczawicznecrew.osrstools.FirestoreHelper');
//       const docRef = FirestoreHelper.getDocumentRef(collection, documentId);
//       return new FirebaseDocumentRef(docRef);
//   }

//   public get(): string | null {
//     return this.docRef.get();
//   }
  

//   public getAsObject(): any | null {
//     const data = this.get();
//     return data ? JSON.parse(data) : null;
//   }
  

//   public update(data: any): void {
//     const jsonData = typeof data === 'string' ? data : JSON.stringify(data);
//     this.docRef.update(jsonData);
//   }

//   public delete(): void {
//     this.docRef.delete();
//   }
  

//   public getJavaObject(): any {
//     return this.docRef;
//   }
// }
