import { Varps } from "../../../api/game/varps";
import { Tile } from "../../../api/model/tile";


export class AlchemyObject {
    constructor(public objectId: number, public tile: Tile, public varbitId: number) { }

    public isSpawned() {
        return Varps.getVarbit(this.varbitId) == 1
    }
}

export const AlchemyObjects = {
    DigweedNorthEast: new AlchemyObject(55396, new Tile(1399, 9331, 0), 11330),
    DigweedSouthEast: new AlchemyObject(55397, new Tile(1399, 9322, 0), 11331),
    DigweedSouthWest: new AlchemyObject(55398, new Tile(1389, 9322, 0), 11332),
    DigweedNorthWest: new AlchemyObject(55399, new Tile(1389, 9331, 0), 11333),
}