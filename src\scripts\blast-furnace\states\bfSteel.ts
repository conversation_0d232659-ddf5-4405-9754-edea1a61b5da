import { createState } from '../../../api/core/script/state'
import { Bank, Withdraw } from '../../../api/game/bank'
import { Dialogue } from '../../../api/game/dialogue'
import { Equipment } from '../../../api/game/equipment'
import { GameObjects } from '../../../api/game/gameObjects'
import { GameTab } from '../../../api/game/gameTab'
import { GeAction } from '../../../api/game/geAction'
import { InputBox } from '../../../api/game/inputBox'
import { Inventory } from '../../../api/game/inventory'
import { Npcs } from '../../../api/game/npcs'
import { Walking } from '../../../api/game/walking'
import { Widgets } from '../../../api/game/widgets'
import { WorldHopping } from '../../../api/game/worldHopping'
import { MenuOpcode } from '../../../api/model/menuOpcode'
import { Tile } from '../../../api/model/tile'
import { TradePackage } from '../../../api/model/tradePackage'
import { ResupplyMuleState } from '../../../api/script-utils/mule/resupplyMuleStrategy'
import { createGeState } from '../../../api/script-utils/states/geStates'
import { getCache, putCache } from '../../../api/utils/cache'
import { PaintTimer } from '../../../api/utils/paintTimer'
import { Time } from '../../../api/utils/time'
import { log } from '../../../api/utils/utils'
import { Player } from '../../../api/wrappers/player'
import { ItemId } from '../../../data/itemId'
import { Locations } from '../../../data/locations'
import { MuleReceiver } from '../../muling/muleReceiver'
import { BfData } from '../bfConstants'
import { BfBarState } from './bfBarState'
import { CoalBagState } from './coalBagState'



export class MakeSteelBarStrategy extends BfBarState {
    private foremanTimer: PaintTimer = new PaintTimer()
    public lastStateChangeTimer: PaintTimer = new PaintTimer()

    onDraw(canvas: any, paint: any): void {
        this.drawText('Coal Bag: ' + getCache("coalBagState"))
    }

    onGameMessage(username: string, message: string): void {
        if(message.includes("coal bag is empty")) {
            putCache("coalBagState", CoalBagState.EMPTY)
        }
        if(message.includes("coal bag contains")) {
            putCache("coalBagState", CoalBagState.FULL)
        }
         
         
    }

    // GE state for buying iron bar supplies
    geState = createGeState(
        () => this.withdrawCoalInitial,
        new ResupplyMuleState(TradePackage.forAccount(MuleReceiver.PACKAGE_RESUPPLY_2m), 358),
        [
            GeAction.item(ItemId.IRON_BAR, 15000)
                .gePrice(0.9, -25)
                .withBank()
                .sell(),   

                  GeAction.item(ItemId.STEEL_BAR, 15000)
                .gePrice(0.9, -25)
                .withBank()
                .sell(),

            GeAction.item(ItemId.IRON_ORE, 13000)
                .gePrice(1.2, 5)
                .continueWhenAmount(12000)
                .withBank()
                .buy(),

            GeAction.item(ItemId.COAL, 13000)
                .gePrice(1.2, 5)
                .continueWhenAmount(12000)
                .withBank()
                .buy(),

            GeAction.item(ItemId.STAMINA_POTION4, 50)
                .gePrice(1.1, 1000)
                .continueWhenAmount(10)
                .withBank()
                .buy(),

            GeAction.item(ItemId.RING_OF_WEALTH_5, 2)
                .gePrice(1.2, 5000)
                .continueWhenAmount(1)
                .withBank()
                .buy(),
        ])

    onBackgroundAction(): void {
        Walking.setRunAuto()
        GameTab.inventory.open()

       if (Dialogue.contains('You must ask the foreman')) {
            this.setState(this.payForemanState)
            return
        }

        if (!this.getStatefulScript().currentState?.name.includes("Paying foreman") && Locations.bankBlastFurnace.distance() < 30 && BfData.coinsInCoffer < 1000) {
            this.setState(this.fillCoffer)
            return
        }

         if (Dialogue.containsOption("Yes, and don't ask again")) {
            Dialogue.goNext("Yes, and don't ask again")
            Time.sleep(600, 1200)
            return
        }

    }

    onCreate(): void {
        putCache("coalBagState", CoalBagState.UNKNOWN)
    }

    onAction(): void {
        if (!WorldHopping.switchToWorld(358)) {
            return
        }

        if (!this.walkBlastFurnace()) {
            return
        }

       

        if (!this.ensureCoffer()) {
            return
        }

        if (!Equipment.withdrawAndEquip(1580)) {
            return
        }

        if(this.coalBagState == CoalBagState.UNKNOWN) {
            this.checkCoalBagState()
            return
        }

        if(BfData.steelBars > 0) {
            this.setState(this.collectBars)
            return
        }

        if(BfData.coalInConveyor < 27) {
            this.setState(this.withdrawCoalInitial)
            return
        }
        
        this.setState(this.withdrawIronAndCoal)

    }
    checkCoalBagState() {

        //withdraw coal bag there:

        if(!Withdraw.id(ItemId.COAL_BAG_12019, 1, 1).withdraw()) {
            return
        }

       Inventory.get().byId(ItemId.COAL_BAG_12019)?.click(57, 4)
       Time.sleep(1200)
    }


    payForeman() {
        if (Locations.bankBlastFurnace.distance() > 35) {
            this.setState(this.withdrawCoalInitial)
            return
        }

        const npc = Npcs.getById(this.FOREMAN)

        //TODO Bank not found ex handler
        if (!Withdraw.builder().id(995).amount(2500).minimumAmount(2500).ensureSpace().withdraw()) return

        if (npc) {
            npc.click(MenuOpcode.NPC_THIRD_OPTION)
            Time.sleep(() => Dialogue.containsOption('Yes'))
        }

        if (Dialogue.isOpen()) {
            if (Dialogue.containsOption('Yes')) {
                Dialogue.goNext('Yes')
                Time.sleep(() => Dialogue.contains('Okay'))
            }
            if (Dialogue.contains('Okay')) {
                this.foremanTimer.reset()
                this.setState(this.withdrawCoalInitial)
            } else {
                if (Time.sleep(() => Dialogue.contains('Okay'))) {
                    this.foremanTimer.reset()
                    this.setState(this.withdrawCoalInitial)
                }
            }
        }
    }

    get coalBagState() {
        if(Bank.isOpen()) {
            const state = BfData.isCoalBagFilled ? CoalBagState.FULL : CoalBagState.EMPTY
            putCache("coalBagState", state)
            return state
        }

        if(getCache("coalBagState") == null) {
           return CoalBagState.UNKNOWN
        }

        return getCache("coalBagState")
    }

    payForemanState = createState("Paying foreman", () => {
        this.payForeman()
    })

    withdrawCoalInitial = createState("Withdrawing coal (initial)", () => {
        if (!this.drinkStaminaPotion(this.geState)) {
            log("stamina")
            return
        }

        if(BfData.steelBars > 0) {
            this.setState(this.collectBars)
            return
        }

        if (!Withdraw.all(this.geState, Withdraw.id(ItemId.COAL_BAG_12019, 1, 1).depositNotedItems().ensureSpace())) {
            return
        }

        if(!Inventory.contains(ItemId.COAL)) {
            if(!Bank.openNearest()) {
                return
            }

            if(!Bank.depositAllExcept([ItemId.COAL_BAG_12019, ItemId.COAL])) {
                return
            }

            Withdraw.id(ItemId.COAL, 27, 27).orState(() => this.geState).withdraw()
            return
        }
        
        if(this.coalBagState == CoalBagState.EMPTY) {
            log("not filled")
            this.clickFillCoalBag()
            Time.sleep(() => this.coalBagState == CoalBagState.FULL)
            return
        }
        
        log("deposit coal because filled state is: " + this.coalBagState)
        this.setState(this.depositCoal)
    })

    depositCoal = createState("Depositing coal", () => {
        if (!this.walkBlastFurnace()) {
            return
        }

        if (Inventory.get().contains(ItemId.COAL)) {
            this.putOre()
            return
        }
 
        if(this.coalBagState == CoalBagState.FULL) {
            this.emptyCoalBag()
            return
        }

        if(BfData.coalInConveyor >= 27) { //TODO sleep and wait for it or set fallback state to withdraw coal.
            this.setState(this.withdrawIronAndCoal)
        }
    })

    withdrawIronAndCoal = createState("Withdrawing iron and coal", () => {
       if (!this.drinkStaminaPotion(this.geState)) {
           return
       }

       if (!Bank.openNearest()) {
           return
       }

       if (!Withdraw.all(this.geState, Withdraw.id(ItemId.COAL_BAG_12019, 1, 1).depositNotedItems().ensureSpace())) {
            return
       }

        if(this.coalBagState !== CoalBagState.FULL) {
            this.clickFillCoalBag()
            Time.sleep(() => this.coalBagState == CoalBagState.FULL)
            return
        }

        if(!Inventory.contains(ItemId.IRON_ORE)) {
            if(!Bank.depositAllExcept([ItemId.COAL_BAG_12019, ItemId.IRON_ORE])) {
                return
            }

            Withdraw.id(ItemId.IRON_ORE, 27, 27).orState(() => this.geState).withdraw()
            return
        }

        log("Continue because state is ", this.coalBagState)
        this.setState(this.depositOres)
    })

    depositOres = createState("Depositing ores", () => {
        if (!this.walkBlastFurnace()) {
            return
        }

        if (Inventory.get().contains(ItemId.IRON_ORE) || Inventory.get().contains(ItemId.COAL)) {
            this.putOre()
            return
        } 
       
        if(this.coalBagState == CoalBagState.FULL && BfData.coalInConveyor < 60) {
            this.emptyCoalBag()
            return
        }

        this.setState(this.collectBars)
    })

    collectBars = createState("Collecting bars", () => {
        if (BfData.steelBars > 0 && Inventory.getFreeSlots() < BfData.steelBars) {
            Bank.openNearest() && Bank.depositAll()
            return
        }

        if (!Walking.walkTo(new Tile(1940, 4962, 0), 0) ) {
            return
        }

        if (!Time.sleep(() => BfData.steelBars > 0)) {
           if(BfData.coalInConveyor >= 27) {
               this.setState(this.withdrawIronAndCoal)
           } else {
               this.setState(this.withdrawCoalInitial)
           }
            return
        }
        
        Time.sleepCycles(2)
        GameObjects.getById(this.BAR_DISPENDER).click(MenuOpcode.GAME_OBJECT_FIRST_OPTION)

        if (Time.sleep(() => Widgets.get(********) != null)) {
            Widgets.get(********).click(57, 1)

            if (Time.sleep(3000, 30, () => Inventory.get().contains(ItemId.STEEL_BAR))) {
                if(BfData.coalInConveyor >= 27) {
               this.setState(this.withdrawIronAndCoal)
           } else {
               this.setState(this.withdrawCoalInitial)
           }
            }
            return
        }

    })

    fillCoffer = createState("Filling coffer", () => {
        if (!this.walkBlastFurnace()) {
            return
        }

        if (!Withdraw.all(
            this.geState,
            Withdraw.id(995, 75000, 75000).ensureSpace()
        )) {
            return
        }

        Inventory.get().byId(995).click(25, 0) //use
        Time.sleep(300, 800)
        GameObjects.getById(29330).click(MenuOpcode.ITEM_USE_ON_OBJECT)

        if (Time.sleep(() => InputBox.isOpen)) {
            InputBox.type('100000', 100, true)
            Time.sleep(() => BfData.coinsInCoffer > 2000)
        }

         if (Dialogue.contains('You must ask the foreman')) {
            putCache("payingforeman", true, 30000)
            this.setState(this.payForemanState)
            return
        }

        this.setState(this.withdrawCoalInitial)
    })





}


