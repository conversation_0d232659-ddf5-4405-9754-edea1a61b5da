import { log } from './utils'


export class PersistentLogs {

    private static readonly LOG_DIR = '/storage/emulated/0/Android/data/com.jagex.oldscape.android/files/logs'


    private static readonly PROCESS_LOG_FILE = 'process.log'


    private static ensureLogDirectory(): boolean {
        let success = false;

        try {
            Java.perform(() => {
                const File = Java.use('java.io.File')
                const logDir = File.$new(this.LOG_DIR)

                if (!logDir.exists()) {
                    success = logDir.mkdirs()
                    log(`Created log directory: ${this.LOG_DIR}, success: ${success}`)
                } else {
                    success = true
                    log(`Log directory already exists: ${this.LOG_DIR}`)
                }
            })
        } catch (e) {
            log(`Error creating log directory: ${e}`)
        }

        return success
    }


    public static getProcessLogFilePath(): string {
        return `${this.LOG_DIR}/${this.PROCESS_LOG_FILE}`
    }


    public static writeLine(message: string): boolean {
        if (!this.ensureLogDirectory()) {
            log('Failed to ensure log directory exists')
            return false
        }

        let success = false;
        const currentTime = new Date().toTimeString().split(" ")[0]
        const logEntry = `[${currentTime}] ${message} \n`

        try {
            Java.perform(() => {
                log(`log write 1`)
                const File = Java.use('java.io.File')
                const FileOutputStream = Java.use('java.io.FileOutputStream')
                const logFile = File.$new(this.getProcessLogFilePath())
                if (!logFile.getParentFile().exists()) {
                    logFile.getParentFile().mkdirs()
                }

                const fos = FileOutputStream.$new(logFile, true)
                const bytes = Java.use('java.lang.String').$new(logEntry).getBytes()
                fos.write(bytes)
                fos.close()
                success = true
            })
        } catch (e) {
            log(`Error writing to process log: ${e}`)
        }

        return success
    }


    public static readLines(): string[] {
        const logs: string[] = []

        try {
            Java.perform(() => {
                const File = Java.use('java.io.File')
                const FileReader = Java.use('java.io.FileReader')
                const BufferedReader = Java.use('java.io.BufferedReader')

                const logFile = File.$new(this.getProcessLogFilePath())

                if (!logFile.exists()) {
                    log(`Process log file does not exist: ${this.getProcessLogFilePath()}`)
                    return
                }

                const fileReader = FileReader.$new(logFile)
                const bufferedReader = BufferedReader.$new(fileReader)

                let line: string
                while ((line = bufferedReader.readLine()) !== null) {
                    logs.push(line)
                }

                bufferedReader.close()
                log(`Read ${logs.length} log entries from process log`)
            })
        } catch (e) {
            log(`Error reading process logs: ${e}`)
        }

        return logs
    }
}
