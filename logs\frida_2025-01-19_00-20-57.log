2025-01-19 00:20:57,482 - INFO - Connected to device: <PERSON><PERSON>(id="socket@**************:27042", name="**************:27042", type='remote')
2025-01-19 00:20:57,483 - INFO - Available processes:
2025-01-19 00:20:58,689 - INFO - Process(pid=15829, name="Gadget", parameters={})
2025-01-19 00:21:03,647 - INFO - <PERSON><PERSON><PERSON> loaded successfully
2025-01-19 00:21:52,405 - INFO - Reloading script...
2025-01-19 00:21:56,002 - ERROR - <PERSON><PERSON><PERSON> was destroyed - possible app crash
2025-01-19 00:21:56,002 - INFO - Attempting to reconnect...
2025-01-19 00:21:58,002 - INFO - Reconnection attempt 1/3
2025-01-19 00:22:02,991 - INFO - <PERSON><PERSON><PERSON> loaded successfully
2025-01-19 00:22:02,993 - INFO - Successfully reconnected
2025-01-19 00:22:02,993 - INFO - Previous script unloaded.
2025-01-19 00:22:09,157 - INFO - <PERSON><PERSON><PERSON> loaded successfully
2025-01-19 00:22:09,158 - INFO - <PERSON><PERSON><PERSON> reloaded successfully.
2025-01-19 00:22:09,159 - INFO - Reloading script...
2025-01-19 00:22:37,174 - ERROR - Script was destroyed - possible app crash
2025-01-19 00:22:37,175 - INFO - Attempting to reconnect...
2025-01-19 00:22:39,176 - INFO - Reconnection attempt 1/3
2025-01-19 00:22:44,738 - INFO - Script loaded successfully
2025-01-19 00:22:44,741 - INFO - Successfully reconnected
2025-01-19 00:22:44,741 - INFO - Previous script unloaded.
2025-01-19 00:22:58,956 - INFO - Script loaded successfully
2025-01-19 00:22:58,958 - INFO - Script reloaded successfully.
2025-01-19 00:23:00,031 - INFO - Reloading script...
2025-01-19 00:23:09,510 - ERROR - Script was destroyed - possible app crash
2025-01-19 00:23:09,510 - INFO - Attempting to reconnect...
2025-01-19 00:23:11,511 - INFO - Reconnection attempt 1/3
2025-01-19 00:23:15,900 - ERROR - Script error: Error: Module parse failed: Unexpected token (63:8)
File was processed with these loaders:
 * ./node_modules/ts-loader/index.js
You may need an additional loader to handle the result of these loaders.
|     switchProxy(proxy) {
|         proxyInject_1.ProxyInjector.
>         ;
|     }
|     setNeutralProxy() { |  Error: Module parse failed: Unexpected token (63:8)
File was processed with these loaders:
 * ./node_modules/ts-loader/index.js
You may need an additional loader to handle the result of these loaders.
|     switchProxy(proxy) {
|         proxyInject_1.ProxyInjector.
>         ;
|     }
|     setNeutralProxy() {
    at ./src/core/proxyHandler.ts (/script1.js:15849)
    at call (native)
    at __webpack_require__ (/script1.js:50384)
    at ./src/api/utils/webUtils.ts (/script1.js:11818)
    at call (native)
    at __webpack_require__ (/script1.js:50384)
    at ./src/data/wikiPrices.ts (/script1.js:29327)
    at call (native)
    at __webpack_require__ (/script1.js:50384)
    at ./src/api/game/geAction.ts (/script1.js:6015)
    at call (native)
    at __webpack_require__ (/script1.js:50384)
    at ./src/api/game/equipment.ts (/script1.js:5469)
    at call (native)
    at __webpack_require__ (/script1.js:50384)
    at ./src/data/teleport.ts (/script1.js:28978)
    at call (native)
    at __webpack_require__ (/script1.js:50384)
    at ./src/api/game/walking.ts (/script1.js:8148)
    at call (native)
    at __webpack_require__ (/script1.js:50384)
    at ./src/api/game/dialogue.ts (/script1.js:5267)
    at call (native)
    at __webpack_require__ (/script1.js:50384)
    at ./src/api/model/webNode.ts (/script1.js:9450)
    at call (native)
    at __webpack_require__ (/script1.js:50384)
    at ./src/data/webData.ts (/script1.js:29229)
    at call (native)
    at __webpack_require__ (/script1.js:50384)
    at ./src/api/core/webPathFinder.ts (/script1.js:4093)
    at call (native)
    at __webpack_require__ (/script1.js:50384)
    at ./src/data/locations.ts (/script1.js:28747)
    at call (native)
    at __webpack_require__ (/script1.js:50384)
    at ./src/api/game/bank.ts (/script1.js:4440)
    at call (native)
    at __webpack_require__ (/script1.js:50384)
    at ./src/api/game/widgets.ts (/script1.js:8457)
    at call (native)
    at __webpack_require__ (/script1.js:50384)
    at ./src/api/wrappers/widget.ts (/script1.js:13579)
    at call (native)
    at __webpack_require__ (/script1.js:50384)
    at ./src/api/wrappers/game.ts (/script1.js:12347)
    at call (native)
    at __webpack_require__ (/script1.js:50384)
    at ./src/api/wrappers/client.ts (/script1.js:12055)
    at call (native)
    at __webpack_require__ (/script1.js:50384)
    at ./src/api/game/skill.ts (/script1.js:7632)
    at call (native)
    at __webpack_require__ (/script1.js:50384)
    at ./src/api/utils/statsMonitor.ts (/script1.js:11203)
    at call (native)
    at __webpack_require__ (/script1.js:50384)
    at ./src/api/core/script/scriptHandler.ts (/script1.js:3714)
    at call (native)
    at __webpack_require__ (/script1.js:50384)
    at ./src/core/bot.ts (/script1.js:14903)
    at call (native)
    at __webpack_require__ (/script1.js:50384)
    at ./src/api/core/lowCpu.ts (/script1.js:2934)
    at call (native)
    at __webpack_require__ (/script1.js:50384)
    at ./src/core/ui.ts (/script1.js:16702)
    at call (native)
    at __webpack_require__ (/script1.js:50384)
    at ./src/api/utils/utils.ts (/script1.js:11426)
    at call (native)
    at __webpack_require__ (/script1.js:50384)
    at ./src/core/background-tasks/fingerprintSpoofer.ts (/script1.js:14026)
    at call (native)
    at __webpack_require__ (/script1.js:50384)
    at ./src/api/model/accountData.ts (/script1.js:8853)
    at call (native)
    at __webpack_require__ (/script1.js:50384)
    at <anonymous> (/script1.js:50447)
    at <anonymous> (/script1.js:50542)
    at <eval> (/script1.js:50544)
2025-01-19 00:23:15,901 - INFO - Script loaded successfully
2025-01-19 00:23:15,902 - INFO - Successfully reconnected
2025-01-19 00:23:15,902 - INFO - Previous script unloaded.
2025-01-19 00:23:20,264 - ERROR - Script error: Error: Module parse failed: Unexpected token (63:8)
File was processed with these loaders:
 * ./node_modules/ts-loader/index.js
You may need an additional loader to handle the result of these loaders.
|     switchProxy(proxy) {
|         proxyInject_1.ProxyInjector.
>         ;
|     }
|     setNeutralProxy() { |  Error: Module parse failed: Unexpected token (63:8)
File was processed with these loaders:
 * ./node_modules/ts-loader/index.js
You may need an additional loader to handle the result of these loaders.
|     switchProxy(proxy) {
|         proxyInject_1.ProxyInjector.
>         ;
|     }
|     setNeutralProxy() {
    at ./src/core/proxyHandler.ts (/script2.js:15849)
    at call (native)
    at __webpack_require__ (/script2.js:50384)
    at ./src/api/utils/webUtils.ts (/script2.js:11818)
    at call (native)
    at __webpack_require__ (/script2.js:50384)
    at ./src/data/wikiPrices.ts (/script2.js:29327)
    at call (native)
    at __webpack_require__ (/script2.js:50384)
    at ./src/api/game/geAction.ts (/script2.js:6015)
    at call (native)
    at __webpack_require__ (/script2.js:50384)
    at ./src/api/game/equipment.ts (/script2.js:5469)
    at call (native)
    at __webpack_require__ (/script2.js:50384)
    at ./src/data/teleport.ts (/script2.js:28978)
    at call (native)
    at __webpack_require__ (/script2.js:50384)
    at ./src/api/game/walking.ts (/script2.js:8148)
    at call (native)
    at __webpack_require__ (/script2.js:50384)
    at ./src/api/game/dialogue.ts (/script2.js:5267)
    at call (native)
    at __webpack_require__ (/script2.js:50384)
    at ./src/api/model/webNode.ts (/script2.js:9450)
    at call (native)
    at __webpack_require__ (/script2.js:50384)
    at ./src/data/webData.ts (/script2.js:29229)
    at call (native)
    at __webpack_require__ (/script2.js:50384)
    at ./src/api/core/webPathFinder.ts (/script2.js:4093)
    at call (native)
    at __webpack_require__ (/script2.js:50384)
    at ./src/data/locations.ts (/script2.js:28747)
    at call (native)
    at __webpack_require__ (/script2.js:50384)
    at ./src/api/game/bank.ts (/script2.js:4440)
    at call (native)
    at __webpack_require__ (/script2.js:50384)
    at ./src/api/game/widgets.ts (/script2.js:8457)
    at call (native)
    at __webpack_require__ (/script2.js:50384)
    at ./src/api/wrappers/widget.ts (/script2.js:13579)
    at call (native)
    at __webpack_require__ (/script2.js:50384)
    at ./src/api/wrappers/game.ts (/script2.js:12347)
    at call (native)
    at __webpack_require__ (/script2.js:50384)
    at ./src/api/wrappers/client.ts (/script2.js:12055)
    at call (native)
    at __webpack_require__ (/script2.js:50384)
    at ./src/api/game/skill.ts (/script2.js:7632)
    at call (native)
    at __webpack_require__ (/script2.js:50384)
    at ./src/api/utils/statsMonitor.ts (/script2.js:11203)
    at call (native)
    at __webpack_require__ (/script2.js:50384)
    at ./src/api/core/script/scriptHandler.ts (/script2.js:3714)
    at call (native)
    at __webpack_require__ (/script2.js:50384)
    at ./src/core/bot.ts (/script2.js:14903)
    at call (native)
    at __webpack_require__ (/script2.js:50384)
    at ./src/api/core/lowCpu.ts (/script2.js:2934)
    at call (native)
    at __webpack_require__ (/script2.js:50384)
    at ./src/core/ui.ts (/script2.js:16702)
    at call (native)
    at __webpack_require__ (/script2.js:50384)
    at ./src/api/utils/utils.ts (/script2.js:11426)
    at call (native)
    at __webpack_require__ (/script2.js:50384)
    at ./src/core/background-tasks/fingerprintSpoofer.ts (/script2.js:14026)
    at call (native)
    at __webpack_require__ (/script2.js:50384)
    at ./src/api/model/accountData.ts (/script2.js:8853)
    at call (native)
    at __webpack_require__ (/script2.js:50384)
    at <anonymous> (/script2.js:50447)
    at <anonymous> (/script2.js:50542)
    at <eval> (/script2.js:50544)
2025-01-19 00:23:20,268 - INFO - Script loaded successfully
2025-01-19 00:23:20,273 - INFO - Script reloaded successfully.
2025-01-19 00:23:20,273 - INFO - Reloading script...
2025-01-19 00:23:23,786 - ERROR - Script was destroyed - possible app crash
2025-01-19 00:23:23,787 - INFO - Attempting to reconnect...
2025-01-19 00:23:25,787 - INFO - Reconnection attempt 1/3
2025-01-19 00:23:29,791 - ERROR - Script error: Error: Module parse failed: Unexpected token (63:8)
File was processed with these loaders:
 * ./node_modules/ts-loader/index.js
You may need an additional loader to handle the result of these loaders.
|     switchProxy(proxy) {
|         proxyInject_1.ProxyInjector.
>         ;
|     }
|     setNeutralProxy() { |  Error: Module parse failed: Unexpected token (63:8)
File was processed with these loaders:
 * ./node_modules/ts-loader/index.js
You may need an additional loader to handle the result of these loaders.
|     switchProxy(proxy) {
|         proxyInject_1.ProxyInjector.
>         ;
|     }
|     setNeutralProxy() {
    at ./src/core/proxyHandler.ts (/script1.js:15849)
    at call (native)
    at __webpack_require__ (/script1.js:50384)
    at ./src/api/utils/webUtils.ts (/script1.js:11818)
    at call (native)
    at __webpack_require__ (/script1.js:50384)
    at ./src/data/wikiPrices.ts (/script1.js:29327)
    at call (native)
    at __webpack_require__ (/script1.js:50384)
    at ./src/api/game/geAction.ts (/script1.js:6015)
    at call (native)
    at __webpack_require__ (/script1.js:50384)
    at ./src/api/game/equipment.ts (/script1.js:5469)
    at call (native)
    at __webpack_require__ (/script1.js:50384)
    at ./src/data/teleport.ts (/script1.js:28978)
    at call (native)
    at __webpack_require__ (/script1.js:50384)
    at ./src/api/game/walking.ts (/script1.js:8148)
    at call (native)
    at __webpack_require__ (/script1.js:50384)
    at ./src/api/game/dialogue.ts (/script1.js:5267)
    at call (native)
    at __webpack_require__ (/script1.js:50384)
    at ./src/api/model/webNode.ts (/script1.js:9450)
    at call (native)
    at __webpack_require__ (/script1.js:50384)
    at ./src/data/webData.ts (/script1.js:29229)
    at call (native)
    at __webpack_require__ (/script1.js:50384)
    at ./src/api/core/webPathFinder.ts (/script1.js:4093)
    at call (native)
    at __webpack_require__ (/script1.js:50384)
    at ./src/data/locations.ts (/script1.js:28747)
    at call (native)
    at __webpack_require__ (/script1.js:50384)
    at ./src/api/game/bank.ts (/script1.js:4440)
    at call (native)
    at __webpack_require__ (/script1.js:50384)
    at ./src/api/game/widgets.ts (/script1.js:8457)
    at call (native)
    at __webpack_require__ (/script1.js:50384)
    at ./src/api/wrappers/widget.ts (/script1.js:13579)
    at call (native)
    at __webpack_require__ (/script1.js:50384)
    at ./src/api/wrappers/game.ts (/script1.js:12347)
    at call (native)
    at __webpack_require__ (/script1.js:50384)
    at ./src/api/wrappers/client.ts (/script1.js:12055)
    at call (native)
    at __webpack_require__ (/script1.js:50384)
    at ./src/api/game/skill.ts (/script1.js:7632)
    at call (native)
    at __webpack_require__ (/script1.js:50384)
    at ./src/api/utils/statsMonitor.ts (/script1.js:11203)
    at call (native)
    at __webpack_require__ (/script1.js:50384)
    at ./src/api/core/script/scriptHandler.ts (/script1.js:3714)
    at call (native)
    at __webpack_require__ (/script1.js:50384)
    at ./src/core/bot.ts (/script1.js:14903)
    at call (native)
    at __webpack_require__ (/script1.js:50384)
    at ./src/api/core/lowCpu.ts (/script1.js:2934)
    at call (native)
    at __webpack_require__ (/script1.js:50384)
    at ./src/core/ui.ts (/script1.js:16702)
    at call (native)
    at __webpack_require__ (/script1.js:50384)
    at ./src/api/utils/utils.ts (/script1.js:11426)
    at call (native)
    at __webpack_require__ (/script1.js:50384)
    at ./src/core/background-tasks/fingerprintSpoofer.ts (/script1.js:14026)
    at call (native)
    at __webpack_require__ (/script1.js:50384)
    at ./src/api/model/accountData.ts (/script1.js:8853)
    at call (native)
    at __webpack_require__ (/script1.js:50384)
    at <anonymous> (/script1.js:50447)
    at <anonymous> (/script1.js:50542)
    at <eval> (/script1.js:50544)
2025-01-19 00:23:29,792 - INFO - Script loaded successfully
2025-01-19 00:23:29,794 - INFO - Successfully reconnected
2025-01-19 00:23:29,794 - INFO - Previous script unloaded.
2025-01-19 00:23:34,137 - ERROR - Script error: Error: Module parse failed: Unexpected token (63:8)
File was processed with these loaders:
 * ./node_modules/ts-loader/index.js
You may need an additional loader to handle the result of these loaders.
|     switchProxy(proxy) {
|         proxyInject_1.ProxyInjector.
>         ;
|     }
|     setNeutralProxy() { |  Error: Module parse failed: Unexpected token (63:8)
File was processed with these loaders:
 * ./node_modules/ts-loader/index.js
You may need an additional loader to handle the result of these loaders.
|     switchProxy(proxy) {
|         proxyInject_1.ProxyInjector.
>         ;
|     }
|     setNeutralProxy() {
    at ./src/core/proxyHandler.ts (/script2.js:15849)
    at call (native)
    at __webpack_require__ (/script2.js:50384)
    at ./src/api/utils/webUtils.ts (/script2.js:11818)
    at call (native)
    at __webpack_require__ (/script2.js:50384)
    at ./src/data/wikiPrices.ts (/script2.js:29327)
    at call (native)
    at __webpack_require__ (/script2.js:50384)
    at ./src/api/game/geAction.ts (/script2.js:6015)
    at call (native)
    at __webpack_require__ (/script2.js:50384)
    at ./src/api/game/equipment.ts (/script2.js:5469)
    at call (native)
    at __webpack_require__ (/script2.js:50384)
    at ./src/data/teleport.ts (/script2.js:28978)
    at call (native)
    at __webpack_require__ (/script2.js:50384)
    at ./src/api/game/walking.ts (/script2.js:8148)
    at call (native)
    at __webpack_require__ (/script2.js:50384)
    at ./src/api/game/dialogue.ts (/script2.js:5267)
    at call (native)
    at __webpack_require__ (/script2.js:50384)
    at ./src/api/model/webNode.ts (/script2.js:9450)
    at call (native)
    at __webpack_require__ (/script2.js:50384)
    at ./src/data/webData.ts (/script2.js:29229)
    at call (native)
    at __webpack_require__ (/script2.js:50384)
    at ./src/api/core/webPathFinder.ts (/script2.js:4093)
    at call (native)
    at __webpack_require__ (/script2.js:50384)
    at ./src/data/locations.ts (/script2.js:28747)
    at call (native)
    at __webpack_require__ (/script2.js:50384)
    at ./src/api/game/bank.ts (/script2.js:4440)
    at call (native)
    at __webpack_require__ (/script2.js:50384)
    at ./src/api/game/widgets.ts (/script2.js:8457)
    at call (native)
    at __webpack_require__ (/script2.js:50384)
    at ./src/api/wrappers/widget.ts (/script2.js:13579)
    at call (native)
    at __webpack_require__ (/script2.js:50384)
    at ./src/api/wrappers/game.ts (/script2.js:12347)
    at call (native)
    at __webpack_require__ (/script2.js:50384)
    at ./src/api/wrappers/client.ts (/script2.js:12055)
    at call (native)
    at __webpack_require__ (/script2.js:50384)
    at ./src/api/game/skill.ts (/script2.js:7632)
    at call (native)
    at __webpack_require__ (/script2.js:50384)
    at ./src/api/utils/statsMonitor.ts (/script2.js:11203)
    at call (native)
    at __webpack_require__ (/script2.js:50384)
    at ./src/api/core/script/scriptHandler.ts (/script2.js:3714)
    at call (native)
    at __webpack_require__ (/script2.js:50384)
    at ./src/core/bot.ts (/script2.js:14903)
    at call (native)
    at __webpack_require__ (/script2.js:50384)
    at ./src/api/core/lowCpu.ts (/script2.js:2934)
    at call (native)
    at __webpack_require__ (/script2.js:50384)
    at ./src/core/ui.ts (/script2.js:16702)
    at call (native)
    at __webpack_require__ (/script2.js:50384)
    at ./src/api/utils/utils.ts (/script2.js:11426)
    at call (native)
    at __webpack_require__ (/script2.js:50384)
    at ./src/core/background-tasks/fingerprintSpoofer.ts (/script2.js:14026)
    at call (native)
    at __webpack_require__ (/script2.js:50384)
    at ./src/api/model/accountData.ts (/script2.js:8853)
    at call (native)
    at __webpack_require__ (/script2.js:50384)
    at <anonymous> (/script2.js:50447)
    at <anonymous> (/script2.js:50542)
    at <eval> (/script2.js:50544)
2025-01-19 00:23:34,138 - INFO - Script loaded successfully
2025-01-19 00:23:34,139 - INFO - Script reloaded successfully.
2025-01-19 00:23:46,249 - INFO - Reloading script...
2025-01-19 00:23:49,872 - ERROR - Script was destroyed - possible app crash
2025-01-19 00:23:49,873 - INFO - Attempting to reconnect...
2025-01-19 00:23:51,875 - INFO - Reconnection attempt 1/3
