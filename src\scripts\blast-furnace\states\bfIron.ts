import { createState } from '../../../api/core/script/state'
import { Bank, Withdraw } from '../../../api/game/bank'
import { Dialogue } from '../../../api/game/dialogue'
import { Equipment } from '../../../api/game/equipment'
import { GameObjects } from '../../../api/game/gameObjects'
import { GameTab } from '../../../api/game/gameTab'
import { GeAction } from '../../../api/game/geAction'
import { InputBox } from '../../../api/game/inputBox'
import { Inventory } from '../../../api/game/inventory'
import { Npcs } from '../../../api/game/npcs'
import { Walking } from '../../../api/game/walking'
import { Widgets } from '../../../api/game/widgets'
import { WorldHopping } from '../../../api/game/worldHopping'
import { MenuOpcode } from '../../../api/model/menuOpcode'
import { Tile } from '../../../api/model/tile'
import { TradePackage } from '../../../api/model/tradePackage'
import { ResupplyMuleState } from '../../../api/script-utils/mule/resupplyMuleStrategy'
import { createGeState } from '../../../api/script-utils/states/geStates'
import { getCache, putCache } from '../../../api/utils/cache'
import { PaintTimer } from '../../../api/utils/paintTimer'
import { Time } from '../../../api/utils/time'
import { Player } from '../../../api/wrappers/player'
import { ItemId } from '../../../data/itemId'
import { Locations } from '../../../data/locations'
import { MuleReceiver } from '../../muling/muleReceiver'
import { BfData } from '../bfConstants'
import { BfBarState } from './bfBarState'



export class MakeIronBarStrategy extends BfBarState {
    private foremanTimer: PaintTimer = new PaintTimer()
    public lastStateChangeTimer: PaintTimer = new PaintTimer()

    // GE state for buying iron bar supplies
    geState = createGeState(
        () => this.withdrawIronOre,
        new ResupplyMuleState(TradePackage.forAccount(MuleReceiver.PACKAGE_RESUPPLY_2m), 358),
        [
            GeAction.item(ItemId.IRON_BAR, 15000)
                .gePrice(0.9, -25)
                .withBank()
                .sell(),

            GeAction.item(ItemId.IRON_ORE, 6500)
                .gePrice(1.2, 5)
                .continueWhenAmount(1000)
                .withBank()
                .buy(),

            GeAction.item(ItemId.STAMINA_POTION4, 50)
                .gePrice(1.1, 1000)
                .continueWhenAmount(10)
                .withBank()
                .buy(),

            GeAction.item(ItemId.RING_OF_WEALTH_5, 2)
                .gePrice(1.2, 5000)
                .continueWhenAmount(1)
                .withBank()
                .buy(),
        ])

    onBackgroundAction(): void {
        Walking.setRunAuto()
        GameTab.inventory.open()

       if (Dialogue.contains('You must ask the foreman')) {
            this.setState(this.payForemanState)
            return
        }

        if (this.getStatefulScript().currentState?.name.includes("Paying foreman") && Locations.bankBlastFurnace.distance() < 30 && BfData.coinsInCoffer < 1000) {
            this.setState(this.fillCoffer)
            return
        }

    }

    onAction(): void {
        if (!WorldHopping.switchToWorld(358)) {
            return
        }

        if (!this.walkBlastFurnace()) {
            return
        }

        if (Dialogue.containsOption("Yes, and don't ask again")) {
            Dialogue.goNext("Yes, and don't ask again")
            Time.sleep(600, 1200)
            return
        }

        if (!this.ensureCoffer()) {
            return
        }

        if (!Equipment.withdrawAndEquip(1580)) {
            return
        }

        this.setState(this.withdrawIronOre)

    }


    payForeman() {
        if (Locations.bankBlastFurnace.distance() > 35) {
            this.setState(this.withdrawIronOre)
            return
        }

        const npc = Npcs.getById(this.FOREMAN)

        //TODO Bank not found ex handler
        if (!Withdraw.builder().id(995).amount(2500).minimumAmount(2500).ensureSpace().withdraw()) return

        if (npc) {
            npc.click(MenuOpcode.NPC_THIRD_OPTION)
            Time.sleep(() => Dialogue.containsOption('Yes'))
        }

        if (Dialogue.isOpen()) {
            if (Dialogue.containsOption('Yes')) {
                Dialogue.goNext('Yes')
                Time.sleep(() => Dialogue.contains('Okay'))
            }
            if (Dialogue.contains('Okay')) {
                this.foremanTimer.reset()
                this.setState(this.withdrawIronOre)
            } else {
                if (Time.sleep(() => Dialogue.contains('Okay'))) {
                    this.foremanTimer.reset()
                    this.setState(this.withdrawIronOre)
                }
            }
        }
    }

    payForemanState = createState("Paying foreman", () => {
        this.payForeman()
    })

    withdrawIronOre = createState("Withdrawing iron ore", () => {
        if (!this.drinkStaminaPotion(this.geState)) {
            return
        }

        if (!Withdraw.all(this.geState, Withdraw.id(ItemId.IRON_ORE, 28).minimumAmount(1).depositNotedItems().ensureSpace())) {
            return
        }

        this.setState(this.depositIronOre)
    })

    depositIronOre = createState("Depositing iron ore", () => {
        if (!this.walkBlastFurnace()) {
            return
        }


        if (BfData.ironBars > 0) {
            this.setState(this.collectIronBars)
            return
        }

        if (Inventory.get().contains(ItemId.IRON_ORE)) {
            this.putOre()
            return
        }
        this.setState(this.collectIronBars)
    })

    collectIronBars = createState("Collecting iron bars", () => {
        if (BfData.ironBars > 0 && Inventory.getFreeSlots() < BfData.ironBars) {
            Bank.openNearest() && Bank.depositAll()
            return
        }

        if (!Walking.walkTo(new Tile(1940, 4962, 0), 0) ) {
            return
        }

        if (!Time.sleep(() => BfData.ironBars > 0 && BfData.isCollectionReady)) {
            this.setState(this.withdrawIronOre)
            return
        }
        
        Time.sleepCycles(2)
        GameObjects.getById(this.BAR_DISPENDER).click(MenuOpcode.GAME_OBJECT_FIRST_OPTION)

        if (Time.sleep(() => Widgets.get(********) != null)) {
            Widgets.get(********).click(57, 1)

            if (Time.sleep(3000, 30, () => Inventory.get().contains(ItemId.IRON_BAR))) {
                this.setState(this.withdrawIronOre)
            }
            return
        }

    })

    fillCoffer = createState("Filling coffer", () => {
        if (!this.walkBlastFurnace()) {
            return
        }

        if (!Withdraw.all(
            this.geState,
            Withdraw.id(995, 75000, 75000).ensureSpace()
        )) {
            return
        }

        Inventory.get().byId(995).click(25, 0) //use
        Time.sleep(300, 800)
        GameObjects.getById(29330).click(MenuOpcode.ITEM_USE_ON_OBJECT)

        if (Time.sleep(() => InputBox.isOpen)) {
            InputBox.type('100000', 100, true)
            Time.sleep(() => BfData.coinsInCoffer > 2000)
        }
        this.setState(this.withdrawIronOre)
    })





}


