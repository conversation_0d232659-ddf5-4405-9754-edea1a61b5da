import { DreambotScript } from '../../api/core/script/dreambotScript'
import { createState } from '../../api/core/script/state'
import { Bank } from '../../api/game/bank'
import { Inventory } from '../../api/game/inventory'
import { AccountProps } from '../../api/model/accountData'
import { ItemContainer } from '../../api/model/itemContainer'
import { TradePackage } from '../../api/model/tradePackage'
import { ResupplyMuleState } from '../../api/script-utils/mule/resupplyMuleStrategy'
import { Client } from '../../api/wrappers/client'
import { BotSettings } from '../../botSettings'
import { Bot } from '../../core/bot'
import { ItemId } from '../../data/itemId'
import { MuleReceiver } from '../muling/muleReceiver'

export class DbGGOTR extends DreambotScript {
    resupply = new ResupplyMuleState(TradePackage.forAccount(MuleReceiver.PACKAGE_RESUPPLY_2m), BotSettings.tradeWorldP2p)

    mainState = createState('Main', () => {
        if (Client.gameState < 30) {
            return
        }

        //TODO check if we need to run this script.
        if (false) {
            Bot.scriptHandler.startNext(this)
            return
        }

        if (!Bank.hasCache()) {
            Bank.openNearest()
            return
        }

        const container = Inventory.get().and(Bank.cache)
        if (container.countByIds(995) < 3_500_000) {
            this.mainState.setState(this.resupply)
            return
        }

        this.startDreambotScript('GGOTR')
    })

    onStart(): void {
        this.initWithState(this.mainState)
    }
}
