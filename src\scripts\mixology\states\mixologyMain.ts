import { State, createState } from '../../../api/core/script/state'

import { Walking } from '../../../api/game/walking'

import { GeAction } from '../../../api/game/geAction'
import { DefaultGeActionsState } from '../../../api/script-utils/states/geStates'
import { ItemPredicate } from '../../../data/itemPredicates'
import { ItemId } from '../../../data/itemId'
import { Bank, Withdraw } from '../../../api/game/bank'
import { Equipment } from '../../../api/game/equipment'
import { ResupplyMuleState } from '../../../api/script-utils/mule/resupplyMuleStrategy'
import { TradePackage } from '../../../api/model/tradePackage'
import { MuleReceiver } from '../../muling/muleReceiver'
import { AllBaseMixes, BaseMix, BaseMixes } from '../api/baseMixItem'
import { GameObjects } from '../../../api/game/gameObjects'
import { MenuOpcode } from '../../../api/model/menuOpcode'
import { Time } from '../../../api/utils/time'
import { Varps } from '../../../api/game/varps'
import { log, removeItem, runOnUtilityThread, startNewThread, startUtilityThread } from '../../../api/utils/utils'
import { Inventory } from '../../../api/game/inventory'
import { Tile } from '../../../api/model/tile'
import { Widgets } from '../../../api/game/widgets'
import { Player } from '../../../api/wrappers/player'
import { getCache, putCache } from '../../../api/utils/cache'
import { Item } from '../../../api/model/item'
import { GameTab } from '../../../api/game/gameTab'
import { ItemContainer } from '../../../api/model/itemContainer'
import { Bot } from '../../../core/bot'
import { BaseSubscriber, ServerTickEvent, SoundEffectEvent } from '../../../api/core/script/game-events/eventBus'
import { Random } from '../../../api/utils/random'
import { WorldHopping } from '../../../api/game/worldHopping'
import { AccountProps } from '../../../api/model/accountData'
import { Skill } from '../../../api/game/skill'
import { AlchemyObjects } from '../api/alchemyObject'
import { GameObject } from '../../../api/wrappers/gameObject'
import { Dialogue } from '../../../api/game/dialogue'
import { Npcs } from '../../../api/game/npcs'
import { GiveToMuleState } from '../../../api/script-utils/mule/giveMuleStrategy'
import { BotSettings } from '../../../botSettings'
import { MixologyReward, RewardsInterface } from '../api/rewardsInterface'

export enum ActionType {
    Crystalise = 'Crystalise',
    Homogenise = 'Homogenise',
    Concentrate = 'Concentrate',
}

export type Order = {
    baseMix: BaseMix
    actionType: ActionType
    isDone: boolean
    hasBasePotion: boolean
}

export const TextureIdToActionType = {
    5672: ActionType.Concentrate,
    5673: ActionType.Crystalise,
    5674: ActionType.Homogenise,
}



export class MixologyMain extends State {

    public moxPoints: number = 0
    public agaPoints: number = 0
    public lyePoints: number = 0
    public subscribed = false
    public enhancedPotionSlots: Set<number> = new Set()
    public lastUsedPotionSlot: number = 27

    onCreate() {
        if (!this.subscribed) {
            this.subscribed = true

            const sub = new BaseSubscriber(SoundEffectEvent, (event) => {


                if (event.id == 2655) {
                    runOnUtilityThread(() => {
                        const order = this.getCurrentOrder()
                        const crystaliseButton = GameObjects.getById(55391)
                        log("Sound effect in script, received.", event.id)

                        if (order && order.actionType == ActionType.Crystalise) {
                            if (getCache("crustaliseSleep")) return
                            putCache("crustaliseSleep", true, 1500)

                            Time.sleep(100)
                            crystaliseButton.click(MenuOpcode.GAME_OBJECT_FIRST_OPTION)
                            return
                        }


                        if (order && order.actionType == ActionType.Homogenise && this.getHomogeniseProgress() <= 10) {
                            if (getCache("homogemiseSleep")) return
                            putCache("homogemiseSleep", true, 2500)


                            const homogeniseButton = GameObjects.getById(55390)
                            Time.sleep(300)
                            homogeniseButton.click(MenuOpcode.GAME_OBJECT_FIRST_OPTION)

                            Time.sleep(100)
                            homogeniseButton.click(MenuOpcode.GAME_OBJECT_FIRST_OPTION)


                            return
                        }
                    })
                }
            })

            Bot.eventBus.register(sub)
        }
    }

    onGameMessage(username: string, message: string): void {

        log("Message: ", message)

        if (message.includes('You deposit some') || message.includes('You deposit all')) {
            this.enhancedPotionSlots.clear()
            log("Cleared enhanced potion tracking due to banking")
            this.setState(this.getBasePotions)
            return
        }

        if (message.includes("has matured...")) {
            this.setState(this.pickDigweed)
            return
        }

        if (message.includes("You're not carrying anything that can be")) {
            this.setState(this.getBasePotions)
            return
        }


        // Detect when a potion is already enhanced with digweed
        if (message.includes("This potion has already been improved")) {
            // Mark the potion in the last used slot as enhanced
            this.enhancedPotionSlots.add(this.lastUsedPotionSlot)
            log(`Detected enhanced potion in slot ${this.lastUsedPotionSlot}`)

            // Try to create a new potion
            this.setState(this.createDigweedMix)
            return
        }


        const rewardMatch = message.match(/You are rewarded <col=\w+>(\d+)<\/col>\/<col=\w+>(\d+)<\/col>\/<col=\w+>(\d+)<\/col>/);
        if (rewardMatch) {
            const rewardedMox = parseInt(rewardMatch[1], 10);
            const rewardedAga = parseInt(rewardMatch[2], 10);
            const rewardedLye = parseInt(rewardMatch[3], 10);

            this.moxPoints += rewardedMox;
            this.agaPoints += rewardedAga;
            this.lyePoints += rewardedLye;
        }
    }

    onBackgroundAction(): void {
        Walking.setRunAuto()
    }

    onDraw(canvas: any, paint: any): void {
        // this.getOrders().forEach((order) => {
        //     this.drawText('' + order?.baseMix?.shortName + ' , ' + order.actionType + ', ' + (order.isDone ? ' done' : '') + ', ' + (order.hasBasePotion ? ' hasBase' : ''))
        // })
        this.drawText("Lye: " + this.script.progressTracker.hourRatioString("Lye", this.lyePoints))

        // this.drawText("getconcentrateProgress: " + this.getConcentrateProgress() )
        // this.drawText("current order: " + this.getCurrentOrder()?.baseMix?.shortName + " " + this.getCurrentOrder()?.actionType)
        // this.drawText("wrongPotionForCurrentAction: " + this.hasWrongPotionForCurrentAction())
        // this.drawText("enhancedPotionSlots: " + Array.from(this.enhancedPotionSlots).join(", "))
        // this.drawText("lastUsedPotionSlot: " + this.lastUsedPotionSlot)
    }





    onAction() {
        if (Varps.getVarbit(-1) == 1) {
            this.setState(this.askPermission)
            return
        }

        if (Inventory.contains(ItemId.DIGWEED)) {
            this.setState(this.createDigweedMix)
            return
        }
        this.setState(this.getBasePotions)
    }

    getCurrentOrder(): Order {
        const pending = this.getPendingOrders()
        if (pending.length == 0) {
            return null
        }


        if (this.getHomogeniseProgress() >= 1) {
            return pending.find(o => o.actionType == ActionType.Homogenise) ?? pending[0]
        }

        if (this.getCrystaliseProgress() >= 1) {
            return pending.find(o => o.actionType == ActionType.Crystalise) ?? pending[0]
        }

        if (this.getConcentrateProgress() >= 1) {
            return pending.find(o => o.actionType == ActionType.Concentrate) ?? pending[0]
        }


        return pending[0]
    }

    getPendingOrders(): Order[] {
        return this.getOrders().filter((o) => !o.isDone)
    }

    isCompletedAllOrders(): boolean {
        return this.getOrders().every((o) => o.isDone)
    }

    processingStarted() {
        return this.concentrateStarted() || this.crystaliseStarted() || this.homogeniseStarted()
    }

    needsToRefill() {
        return this.getMoxInHopper() < 40 || this.getAgaInHopper() < 40 || this.getLyeInHopper() < 40
    }


    getRewardPointsMoxResin() {
        return Varps.getConfig(4416)
    }

    getRewardPointsAgaResin() {
        return Varps.getConfig(4415)
    }

    getRewardPointsLyeResin() {
        return Varps.getConfig(4414)
    }


    getConcentrateProgress() {
        return Varps.getVarbit(11327)
    }

    getCrystaliseProgress() {
        return Varps.getVarbit(11328)
    }

    getHomogeniseProgress() {
        return Varps.getVarbit(11329)
    }

    crystaliseStarted() {
        return Varps.getVarbit(11328) > 0 || GameObjects.getById(55391)?.realId != 54892
    }


    homogeniseStarted() {
        return Varps.getVarbit(11329) > 0 || GameObjects.getById(55390)?.realId != 54881
    }

    concentrateStarted() {
        return Varps.getVarbit(11327) > 0 || GameObjects.getById(55389)?.realId != 54870
    }

    getMoxInHopper() {
        return Varps.getVarbit(11431)
    }

    getAgaInHopper() {
        return Varps.getVarbit(11432)
    }

    getLyeInHopper() {
        return Varps.getVarbit(11433)
    }

    getOrders(): Order[] {
        var order1Text = Widgets.getByRootId(882, 2, 2)?.text.toLowerCase()
        var order2Text = Widgets.getByRootId(882, 2, 4)?.text.toLowerCase()
        var order3Text = Widgets.getByRootId(882, 2, 6)?.text.toLowerCase()

        const order1BaseMix = AllBaseMixes.filter((b) => order1Text.includes(b.fullName.toLowerCase()))[0]
        const order2BaseMix = AllBaseMixes.filter((b) => order2Text.includes(b.fullName.toLowerCase()))[0]
        const order3BaseMix = AllBaseMixes.filter((b) => order3Text.includes(b.fullName.toLowerCase()))[0]

        let inventoryItemIds = Inventory.get().items.map((i) => i.id)

        const order1: Order = {
            //@ts-ignore
            actionType: TextureIdToActionType[Widgets.getByRootId(882, 2, 1)?.textureId ?? 0],
            baseMix: order1BaseMix,
            isDone: inventoryItemIds.includes(order1BaseMix.processedItemId),
            hasBasePotion: inventoryItemIds.includes(order1BaseMix.itemId),
        }

        removeItem(inventoryItemIds, order1BaseMix.processedItemId)
        if (!order1.isDone) {
            removeItem(inventoryItemIds, order1BaseMix.itemId)
        }


        const order2: Order = {
            //@ts-ignore
            actionType: TextureIdToActionType[Widgets.getByRootId(882, 2, 3)?.textureId ?? 0],
            baseMix: order2BaseMix,
            isDone: inventoryItemIds.includes(order2BaseMix.processedItemId),
            hasBasePotion: inventoryItemIds.includes(order2BaseMix.itemId),
        }

        removeItem(inventoryItemIds, order2BaseMix.processedItemId)

        if (!order2.isDone) {
            removeItem(inventoryItemIds, order2BaseMix.itemId)
        }

        const order3: Order = {
            //@ts-ignore
            actionType: TextureIdToActionType[Widgets.getByRootId(882, 2, 5)?.textureId ?? 0],
            baseMix: order3BaseMix,
            isDone: inventoryItemIds.includes(order3BaseMix.processedItemId),
            hasBasePotion: inventoryItemIds.includes(order3BaseMix.itemId),
        }


        return [order1, order2, order3]
    }

    createBasePotion(baseMix: BaseMix) {
        if (Inventory.containsByPredicate(i => i.id == baseMix.itemId && !this.enhancedPotionSlots.has(i.slot))) {
            return true
        }

        if (Varps.getVarbit(11339) == baseMix.mixerVarbitValue) {
            const mixingVessel = GameObjects.getById(55395)
            mixingVessel.click(MenuOpcode.GAME_OBJECT_FIRST_OPTION)
            Time.sleepCycles(2)
            return false
        }

        this.pullLevers(baseMix)
        return false
    }

    askPermission = createState('Asking permission', () => {
        if (Dialogue.contains("Suit yourself") || Dialogue.containsOption("Can I have some rewards")) {
            this.setState(this.refineHerbs)
            return
        }

        if (Dialogue.talkTo(Npcs.getById(14099), true, new Tile(1393, 9312, 0))) {
            Dialogue.goNext("I'm sure I can work it out")
            return
        }


    })

    resupply = new ResupplyMuleState(TradePackage.forAccount(MuleReceiver.PACKAGE_RESUPPLY_2m), 308)

    // GE state for buying required items
    geState = new DefaultGeActionsState(() => this, this.resupply, [
        // Herbs for Mox paste
        GeAction.item(() => new Item(this.getHerbForMoxPaste(), 300))
            .gePrice(1.05, 25)
            .withBank()
            .buy(),

        // Herbs for Aga paste
        GeAction.item(() => new Item(this.getHerbForAgaPaste(), 300))
            .gePrice(1.05, 25)
            .withBank()
            .buy(),

        // Herbs for Lye paste
        GeAction.item(() => new Item(this.getHerbForLyePaste(), 300))
            .gePrice(1.05, 25)
            .withBank()
            .buy(),

        // Equipment for teleporting
        GeAction.item(() => new Item(ItemId.RING_OF_WEALTH_5, 2))
            .gePrice(1.15, 5025)
            .continueWhenAmount(1)
            .withBank()
            .buy(),

        GeAction.item(() => new Item(ItemId.AMULET_OF_GLORY6, 2))
            .gePrice(1.15, 3025)
            .continueWhenAmount(1)
            .withBank()
            .buy(),

    ])

    // Banking state to prepare inventory
    doBanking = createState('Banking', () => {
        if (!Bank.openNearest()) {
            return
        }

        // Withdraw required teleport items and coins
        if (
            !Withdraw.all(
                this.geState,
                Withdraw.predicate(ItemPredicate.ringOfWealth, 1).ensureSpace(),
                Withdraw.predicate(ItemPredicate.amuletOfGloryCharged, 1).ensureSpace()
            )
        ) {
            return
        }  
        
        if (
            !Withdraw.all(
                this.resupply,
                Withdraw.id(ItemId.COINS_995, 60000).minimumAmount(30000).ensureSpace()
            )
        ) {
            return
        }

        // Equip the items if they're in inventory
        const ringOfWealth = Inventory.get().getByPredicate(ItemPredicate.ringOfWealth);
        if (ringOfWealth) {
            ringOfWealth.click(MenuOpcode.DEFAULT, 3); // 3 is the "Wear" option
            Time.sleepCycles(1);
        }

        const glory = Inventory.get().getByPredicate(ItemPredicate.amuletOfGloryCharged);
        if (glory) {
            glory.click(MenuOpcode.DEFAULT, 3); // 3 is the "Wear" option
            Time.sleepCycles(1);
        }

        // Continue to the minigame
        this.setState(this.getBasePotions);
    })

    enterMinigame = createState('Entering minigame', () => { })

    leaveMinigame = createState('Leaving minigame', () => { })

    // Check if player is far from minigame and doesn't have required teleport items
    isFarFromMinigameWithoutItems(): boolean {
        const minigameTile = new Tile(1394, 9324, 0);
        const distance = minigameTile.distance();

        // Check if distance is more than 30 and player doesn't have required items
        if (distance > 30) {
            const hasRingOfWealth = Inventory.get().containsByPredicate(ItemPredicate.ringOfWealth) ||
                Equipment.get().containsByPredicate(ItemPredicate.ringOfWealth);

            const hasGlory = Inventory.get().containsByPredicate(ItemPredicate.amuletOfGloryCharged) ||
                Equipment.get().containsByPredicate(ItemPredicate.amuletOfGloryCharged);

            const hasEnoughCoins = Inventory.get().contains(ItemId.COINS_995, 5000);

            return !hasRingOfWealth || !hasGlory || !hasEnoughCoins;
        }

        return false;
    }

    getBasePotions = createState('Getting base potions', () => {
        GameTab.inventory.open()


        if (!WorldHopping.switchToP2pExcept()) {
            return
        }

        if (ItemContainer.allCombined().countWithNoted(ItemId.ALDARIUM) > 100) {
            this.setState(this.giveToMule)
            return
        }

        if (this.getRewardPointsLyeResin() > 6000 && new Tile(1393, 9312, 0).distance() < 15) {
            this.setState(this.claimRewards)
            return
        }

        // Check if we're far from minigame and don't have teleport items
        if (this.isFarFromMinigameWithoutItems()) {
            log("Far from minigame and missing teleport items, going to bank/GE");
            this.setState(this.doBanking);
            return;
        }

        if (Inventory.getFreeSlots() < 10) {
            Bank.openNearest() && Bank.depositAll()
            return
        }

        if (!Walking.walkTo(new Tile(1394, 9324, 0), 6)) {
            return
        }

        if (this.isCompletedAllOrders()) {
            this.setState(this.fullfillOrder)
            return
        }

        if (this.needsToRefill()) {
            this.setState(this.refillHopper)
            return
        }

        if (this.processingStarted()) {
            this.setState(this.processPotions)
            return
        }

        const orders = this.getPendingOrders()

        for (const order of orders) {
            if (order.hasBasePotion || order.isDone) {
                continue
            }
            if (Varps.getVarbit(11339) == order.baseMix.mixerVarbitValue) {
                const mixingVessel = GameObjects.getById(55395)
                mixingVessel.click(MenuOpcode.GAME_OBJECT_FIRST_OPTION)
                Time.sleep(() => Varps.getVarbit(11339) <= 0)
                return
            }

            if (!Walking.walkTo(new Tile(1394, 9324, 0), 0)) {
                return
            }
            this.pullLevers(order.baseMix)
            Time.sleep(2000, () => Varps.getVarbit(11339) == order.baseMix.mixerVarbitValue)
            return
        }

        this.setState(this.processPotions)
    })



    refillHopper = createState('Refilling Hopper', () => {
        // If we have pastes in inventory, click on the hopper

        // Try to withdraw all pastes, if any are missing, go to refineHerbs state
        if (!Withdraw.all(
            this.refineHerbs,
            Withdraw.id(ItemId.MOX_PASTE, 3000).exactAmount().ensureSpace(),
            Withdraw.id(ItemId.AGA_PASTE, 3000).exactAmount().ensureSpace(),
            Withdraw.id(ItemId.LYE_PASTE, 3000).exactAmount().ensureSpace()
        )) {
            log("Did not withdraw.")
            return
        }

        const hopperTile = new Tile(1394, 9321, 0);
        if (!Walking.walkTo(hopperTile, 3)) {
            log("Did not walk")
            return;
        }

        // Click on the hopper
        const hopper = GameObjects.getById(54903);
        if (hopper) {
            hopper.click(MenuOpcode.GAME_OBJECT_FIRST_OPTION);
            Time.sleepCycles(2);

            // Check if we have enough points to proceed to getBasePotions
            if (this.getMoxInHopper() >= 500 && this.getAgaInHopper() >= 500 && this.getLyeInHopper() >= 500) {
                this.setState(this.getBasePotions);
            }
            log("Click or what")
            return;
        }

        // If we don't have pastes in inventory, go to bank
        if (!Bank.openNearest()) {
            log("Did not open bank")
            return
        }


    })

    refineHerbs = createState('Refining herbs', () => {
        // Check if in inventory is any of potions (processed or unprocessed) then drop them
        if (this.hasPotionsInInventory()) {
            this.dropPotionsFromInventory()
            return
        }

        if (Dialogue.contains("Hey! I haven't given you permission")) {
            this.setState(this.askPermission)
            return
        }

        GameTab.inventory.open()

        const refinerTile = new Tile(1399, 9312, 0)
        const refinerObject = GameObjects.getById(54904)

        // If we have herbs in inventory, use them on the refiner
        if (Inventory.get().countByPredicate(item => this.isHerbItem(item.id)) > 0) {
            if (refinerTile.distance() > 5 || !refinerObject) {
                // Walk to the refiner if we're not close enough
                if (!Walking.walkTo(refinerTile, 3)) {
                    return
                }
            } else {
                refinerObject.click(MenuOpcode.GAME_OBJECT_FIRST_OPTION)
                Time.sleepCycles(1)
                return
            }
        } else {
            // Go to bank to get more herbs
            if (!Bank.cache && !Bank.openNearest()) {
                return
            }

            const needMoxPaste = !ItemContainer.allCombined().contains(ItemId.MOX_PASTE, 3000)
            const needAgaPaste = !ItemContainer.allCombined().contains(ItemId.AGA_PASTE, 3000)
            const needLyePaste = !ItemContainer.allCombined().contains(ItemId.LYE_PASTE, 3000)

            log(`Need to create pastes: Mox=${needMoxPaste}, Aga=${needAgaPaste}, Lye=${needLyePaste}`)

            // Withdraw herbs based on what pastes we need
            if (needMoxPaste) {
                Withdraw.all(this.geState, Withdraw.id(this.getHerbForMoxPaste(), 28).ensureSpace())
                return
            }

            if (needAgaPaste) {
                Withdraw.all(this.geState, Withdraw.id(this.getHerbForAgaPaste(), 28).ensureSpace())
                return
            }

            if (needLyePaste) {
                Withdraw.all(this.geState, Withdraw.id(this.getHerbForLyePaste(), 28).ensureSpace())
                return
            }

            // If we have enough of all pastes, go back to refill hopper
            this.setState(this.refillHopper)
            return
        }
    })

    // Helper method to check if an item is a herb
    isHerbItem(itemId: number): boolean {
        return [
            // Mox paste herbs
            ItemId.GUAM_LEAF,
            ItemId.MARRENTILL,
            ItemId.TARROMIN,
            ItemId.HARRALANDER,

            // Aga paste herbs
            ItemId.IRIT_LEAF,
            ItemId.HUASCA,
            ItemId.CADANTINE,
            ItemId.LANTADYME,
            ItemId.DWARF_WEED,
            ItemId.TORSTOL,

            // Lye paste herbs
            ItemId.RANARR_WEED,
            ItemId.TOADFLAX,
            ItemId.AVANTOE,
            ItemId.KWUARM,
            ItemId.SNAPDRAGON
        ].includes(itemId)
    }

    // Get the most efficient herb for Mox paste
    getHerbForMoxPaste(): number {
        return ItemId.HARRALANDER
    }

    // Get the most efficient herb for Aga paste
    getHerbForAgaPaste(): number {
        return ItemId.LANTADYME
    }

    // Get the most efficient herb for Lye paste
    getHerbForLyePaste(): number {
        return ItemId.AVANTOE
    }

    // Helper method to check if there are multiple unprocessed potions in inventory
    hasMultipleUnprocessedPotions(): boolean {
        const inventory = Inventory.get()
        let unprocessedCount = 0

        for (const baseMix of AllBaseMixes) {
            if (inventory.contains(baseMix.itemId)) {
                unprocessedCount++
                if (unprocessedCount > 1) {
                    return true
                }
            }
        }

        return false
    }

    // Helper method to check if we have a wrong potion that could be incorrectly processed
    hasWrongPotionForCurrentAction(): boolean {
        if (!this.hasMultipleUnprocessedPotions()) {
            return false
        }

        if (this.processingStarted()) {
            return false
        }

        const order = this.getCurrentOrder()
        if (!order) {
            return false
        }

        const unprocessedPotionIds = AllBaseMixes.map(mix => mix.itemId)


        for (const item of Inventory.get().items) {
            if (unprocessedPotionIds.includes(item.id) && item.id != order.baseMix.itemId) {
                return true
            }
            if (unprocessedPotionIds.includes(item.id) && item.id == order.baseMix.itemId) {
                return false
            }
        }

        return false
    }

    // Helper method to check if there are any potions in the inventory
    hasPotionsInInventory(): boolean {
        const inventory = Inventory.get()

        // Check for unprocessed potions
        const unprocessedPotionIds = AllBaseMixes.map(mix => mix.itemId)
        if (inventory.containsAny(...unprocessedPotionIds)) {
            return true
        }

        // Check for processed potions
        const processedPotionIds = AllBaseMixes.map(mix => mix.processedItemId)
        if (inventory.containsAny(...processedPotionIds)) {
            return true
        }

        return false
    }

    // Helper method to drop all potions from inventory
    dropPotionsFromInventory(): void {
        GameTab.inventory.open()

        // Get all unprocessed and processed potion IDs
        const unprocessedPotionIds = AllBaseMixes.map(mix => mix.itemId)
        const processedPotionIds = AllBaseMixes.map(mix => mix.processedItemId)
        const allPotionIds = [...unprocessedPotionIds, ...processedPotionIds]

        // Drop all potions
        log(`Dropping potions from inventory`)
        Inventory.destroyAllByIds(...allPotionIds)
    }

    processPotions = createState('Processing potions', () => {
        const order = this.getCurrentOrder()

        if (this.isCompletedAllOrders()) {
            this.setState(this.fullfillOrder)
            return
        }

        if (Player.local.isAnimating) {
            putCache('isAnimating', true, 1200)
        }

        let actionType = order.actionType

        if (this.homogeniseStarted()) actionType = ActionType.Homogenise
        if (this.crystaliseStarted()) actionType = ActionType.Crystalise
        if (this.concentrateStarted()) actionType = ActionType.Concentrate

        log("actionType: ", actionType)
        if (actionType == ActionType.Concentrate) {
            
            if (this.getConcentrateProgress() >= 11 && getCache("lastClick")) {
                Time.sleep(() => this.getConcentrateProgress() <= 0)
                Time.sleepCycles(2)
                return
            }

            const concentrateButton = GameObjects.getById(55389)

            // Check if we need to use a specific potion on the object
            if (this.hasWrongPotionForCurrentAction()) {
                const potionItem = Inventory.getById(order.baseMix.itemId)
                if (potionItem) {
                    potionItem.useOnObject(concentrateButton)
                    putCache("lastClick", true, 6000)
                    Time.waitForAnimation()
                    putCache("lastClick", true, 6000)
                    return
                }
            } else {
                // Otherwise just click the object
                concentrateButton.click(MenuOpcode.GAME_OBJECT_FIRST_OPTION)
                putCache("lastClick", true, 6000)
                Time.sleep(200, 900)
                putCache("lastClick", true, 6000)
            }

            return
        }

        if (actionType == ActionType.Homogenise) {
            if (getCache('isAnimating')) {
                return
            }

            log('Animation?: ', Player.local.animation)
            const homogeniseButton = GameObjects.getById(55390)

            // Check if we need to use a specific potion on the object
            if (this.hasWrongPotionForCurrentAction()) {
                const potionItem = Inventory.getById(order.baseMix.itemId)
                if (potionItem) {
                    potionItem.useOnObject(homogeniseButton)
                    Time.waitForAnimation()
                    return
                }
            } else {
                // Otherwise just click the object
                homogeniseButton.click(MenuOpcode.GAME_OBJECT_FIRST_OPTION)
                Time.waitForAnimation()
            }
            return
        }

        if (actionType == ActionType.Crystalise) {
            if (getCache('isAnimating')) {
                return
            }

            if (!Walking.walkTo(new Tile(1392, 9325, 0), 0)) {
                return
            }

            const crystaliseButton = GameObjects.getById(55391)

            // Check if we need to use a specific potion on the object
            if (this.hasWrongPotionForCurrentAction()) {
                const potionItem = Inventory.getById(order.baseMix.itemId)
                if (potionItem) {
                    potionItem.useOnObject(crystaliseButton)
                    Time.waitForAnimation()
                    return
                }
            } else {
                // Otherwise just click the object
                crystaliseButton.click(MenuOpcode.GAME_OBJECT_FIRST_OPTION)
                Time.waitForAnimation()
            }
            return
        }


        log("Unknown action type.")
    })

    pickDigweed = createState('Picking digweed', () => {
        if (Inventory.contains(ItemId.DIGWEED)) {
            this.setState(this.createDigweedMix)
            return
        }

        let digweed: GameObject = null

        if (AlchemyObjects.DigweedNorthEast.isSpawned()) {
            digweed = GameObjects.getAtTile(AlchemyObjects.DigweedNorthEast.tile)[0]
        }
        else if (AlchemyObjects.DigweedSouthEast.isSpawned()) {
            digweed = GameObjects.getAtTile(AlchemyObjects.DigweedSouthEast.tile)[0]
        }
        else if (AlchemyObjects.DigweedSouthWest.isSpawned()) {
            digweed = GameObjects.getAtTile(AlchemyObjects.DigweedSouthWest.tile)[0]
        }
        else if (AlchemyObjects.DigweedNorthWest.isSpawned()) {
            digweed = GameObjects.getAtTile(AlchemyObjects.DigweedNorthWest.tile)[0]
        }

        if (digweed == null) {
            log("No digweed found.")
            return
        }
        digweed.click(MenuOpcode.GAME_OBJECT_FIRST_OPTION)

        Time.sleep(1000, 1600)


    })

    createDigweedMix = createState('Creating digweed mix', () => {
        if (!Inventory.contains(ItemId.DIGWEED)) {
            this.setState(this.getBasePotions)
            return
        }

        const mixToMake = Skill.HERBLORE.getCurrentLevel() >= 81 ? BaseMixes.MAL : BaseMixes.MLL
        if (!this.createBasePotion(mixToMake)) {
            return
        }


        // Use digweed on the potion
        const potionItem = Inventory.getByPredicate(i => i.id == mixToMake.itemId && !this.enhancedPotionSlots.has(i.slot))
        if (potionItem) {
            // Update the last used potion slot
            this.lastUsedPotionSlot = potionItem.slot//targetSlot
            log(`Using digweed on potion in slot ${potionItem.slot}`)

            potionItem.useOnItem(ItemId.DIGWEED)
            Time.sleep(1000, 1600)
        }
    })

    fullfillOrder = createState('Fullfilling order', () => {


        const processedIds = this.getOrders().map(o => o.baseMix.processedItemId)

        if (Inventory.containsAny(...processedIds)) {
            GameObjects.getById(54917)?.click(MenuOpcode.GAME_OBJECT_FIRST_OPTION)
            Time.sleepCycles(2)
            return
        }

        // Check enhancedPotionSlots and remove slots that no longer have items
        if (this.enhancedPotionSlots.size > 0) {
            const inventory = Inventory.get()
            const slotsToRemove: number[] = []

            // Identify slots to remove
            this.enhancedPotionSlots.forEach(slot => {
                // Check if there's no item in this slot
                if (!inventory.getByPredicate(item => item.slot === slot)) {
                    slotsToRemove.push(slot)
                }
            })

            // Remove identified slots
            if (slotsToRemove.length > 0) {
                slotsToRemove.forEach(slot => {
                    this.enhancedPotionSlots.delete(slot)
                    log(`Removed slot ${slot} from enhancedPotionSlots as it no longer contains an item`)
                })
            }
        }

        this.setState(this.getBasePotions)
    })

    pullLevers(baseMix: BaseMix) {

        if (!Walking.walkTo(new Tile(1394, 9324, 0), 0)) {
            return
        }

        Time.sleepCycles(1)

        baseMix.shortName.split('').forEach((l) => {
            switch (l) {
                case 'A':
                    const agaLever = GameObjects.getById(54867)
                    agaLever.click(MenuOpcode.GAME_OBJECT_FIRST_OPTION)
                    Time.sleepCycles(1)
                    break
                case 'M':
                    const moxLever = GameObjects.getById(54868)
                    moxLever.click(MenuOpcode.GAME_OBJECT_FIRST_OPTION)
                    Time.sleepCycles(1)
                    break
                case 'L':
                    const lyeLever = GameObjects.getById(54869)
                    lyeLever.click(MenuOpcode.GAME_OBJECT_FIRST_OPTION)
                    Time.sleepCycles(1)
                    break
            }
        })
    }

    giveToMule = new GiveToMuleState(TradePackage.forAccount(MuleReceiver.PACKAGE_GIVE), BotSettings.tradeWorldP2p, [new Item(995, 2_000_000)], [new Item(ItemId.ALDARIUM, 50000)])


    claimRewards = createState('Claiming rewards', () => {
        //9 = chugging barrel

        if (this.getRewardPointsLyeResin() < 90 || this.getRewardPointsMoxResin() < 80) {

            if (ItemContainer.allCombined().countWithNoted(ItemId.ALDARIUM) > 100) {
                this.setState(this.giveToMule)
                return
            }

            this.setState(this.getBasePotions)
            return
        }

        if (new Tile(1393, 9312, 0).distance() > 15) {
            this.setDefaultState()
            return
        }

        const aldariumWidget = Widgets.getPackedChild(********, 132)

        if (!aldariumWidget?.isRendered) {
            Npcs.getById(14099)?.click(MenuOpcode.NPC_THIRD_OPTION)
            Time.sleep(() => Widgets.getPackedChild(********, 132)?.isRendered == true)
            return
        }

        if (RewardsInterface.getSelectedReward() != MixologyReward.Aldarium) {
            aldariumWidget.click(57, 1)
            Time.sleepCycles(1)
            return
        }

        const buy50 = Widgets.get(53674012)
        const buy10 = Widgets.get(53674011)
        const buy5 = Widgets.get(53674010)
        const buy1 = Widgets.get(53674009)


        buy50?.click(57, 1)
        buy10?.click(57, 1)
        buy5?.click(57, 1)
        buy1?.click(57, 1)
        Time.sleepCycles(1)

    })


}
