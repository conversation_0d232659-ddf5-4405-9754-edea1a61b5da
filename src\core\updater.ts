import { killCurrentProcess, log, toast } from "../api/utils/utils";
import { WebUtils } from "../api/utils/webUtils";

// const baseUrl = "http://192.168.10.163:5000"
const baseUrl =  "http://10.0.0.2:5000"
const bundlePath = "/sdcard/Android/data/com.jagex.oldscape.android/files/bundle.js"

export function checkForUpdates() {
    toast("Checking for updates...")
    const installedHash = getInstalledBundleHash()
    log("Installed bundle hash:", installedHash)
    const newestHash = getNewestBundleHash()

    
    log("Newest bundle hash:", newestHash)
    log("Installed bundle hash:", installedHash)

    if (installedHash != newestHash) {
        updateBundle()
    } else {
        toast("Bundle is up to date")
}
}

export function updateBundle() {

    toast("Downloading update...")
    const url = `${baseUrl}/api/update/bundle/download`
    const response = WebUtils.getLarge(url, 120_000)
    toast("Downloaded bundle")
    
    try {
        log("Test 1")
        const File = Java.use("java.io.File");
        const FileOutputStream = Java.use("java.io.FileOutputStream");
        const String = Java.use("java.lang.String");
        log("File create new one.")
        
        // Create file object
        const bundleFile = File.$new(bundlePath);
        
        log("Bundle file created:", bundleFile)
        const parentDir = bundleFile.getParentFile();
        if (parentDir && !parentDir.exists()) {
            log("Parent directory created:", parentDir)
            parentDir.mkdirs();
        }
        
        // Write the response content to file
        const fos = FileOutputStream.$new(bundleFile);
        log("File output stream created:", fos)

        const contentBytes = String.$new(response).getBytes("UTF-8");
        
        fos.write(contentBytes);
        log("Content written to file")
        fos.close();
        
        log("Bundle updated successfully at:", bundlePath);
        killCurrentProcess("Bundle updated successfully")
    } catch (error) {
        log("Error updating bundle:", error);
        throw error;
    }
}

export function getNewestBundleHash(): string | null {
    const url = `${baseUrl}/api/update/bundle/version`
    const r = WebUtils.get(url)
    log("Newest bundle hash:", r)
    const response = JSON.parse(r)
    log("Newest bundle hash:", response.hash)
    return response.hash
}

export function getInstalledBundleHash(): string | null {
    log("Getting bundle hash")
    
    try {
        // Get Java classes we need
        const File = Java.use("java.io.File");
        const FileInputStream = Java.use("java.io.FileInputStream");
        const MessageDigest = Java.use("java.security.MessageDigest");
        const ByteArrayOutputStream = Java.use("java.io.ByteArrayOutputStream");
        
        // Create file object
        const bundleFile = File.$new(bundlePath);
        
        // Check if file exists
        if (!bundleFile.exists()) {
            log("Bundle file does not exist:", bundlePath);
            return null;
        }
        
        // Read file bytes
        const fis = FileInputStream.$new(bundleFile);
        const baos = ByteArrayOutputStream.$new();
        
        const buffer = Java.array('byte', new Array(8192).fill(0));
        let bytesRead = 0;
        
        while ((bytesRead = fis.read(buffer)) !== -1) {
            baos.write(buffer, 0, bytesRead);
        }
        
        const fileBytes = baos.toByteArray();
        fis.close();
        baos.close();
        
        // Calculate MD5 hash
        const md5 = MessageDigest.getInstance("MD5");
        const hashBytes = md5.digest(fileBytes);
        
        // Convert to hex string
        let hexString = "";
        for (let i = 0; i < hashBytes.length; i++) {
            const hex = (hashBytes[i] & 0xFF).toString(16);
            hexString += hex.length === 1 ? "0" + hex : hex;
        }
        
        return hexString;
        
    } catch (error) {
        log("Error calculating bundle hash:", error);
        return null;
    }
}