import { ItemId } from "../../../data/itemId";

export class BaseMix {
  constructor(public shortName: string, public fullName: string, public mixerVarbitValue: number, public itemId?: number, public processedItemId?: number) { // Made IDs optional for entries without them initially

  }
}

export const BaseMixes = {
    AAA: new BaseMix('AAA','Alco-augmentator',  4, ItemId.ALCOAUGMENTATOR, ItemId.ALCOAUGMENTATOR_30024),
    MMM: new BaseMix('MMM','Mammoth-might mix',   1, ItemId.MAMMOTHMIGHT_MIX, ItemId.MAMMOTHMIGHT_MIX_30021),
    LLL: new BaseMix('LLL','Liplack liquor',  7, ItemId.LIPLACK_LIQUOR, ItemId.LIPLACK_LIQUOR_30027),
    MMA: new BaseMix('MMA','Mystic mana amalgam',  2, ItemId.MYSTIC_MANA_AMALGAM, ItemId.MYSTIC_MANA_AMALGAM_30022),
    MML: new BaseMix('M<PERSON>','<PERSON>\'s moonlight',  3, ItemId.MARLEYS_MOONLIGHT, ItemId.MARLEYS_MOONLIGHT_30023),
    AAM: new BaseMix('AAM','Azure aura mix',  5, ItemId.AZURE_AURA_MIX, ItemId.AZURE_AURA_MIX_30026),
    ALA: new BaseMix('ALA','Aqualux amalgam',  6, ItemId.AQUALUX_AMALGAM, ItemId.AQUALUX_AMALGAM_30025),
    MLL: new BaseMix('MLL','Megalite liquid',  8, ItemId.MEGALITE_LIQUID, ItemId.MEGALITE_LIQUID_30029),
    ALL: new BaseMix('ALL','Anti-leech lotion',  9, ItemId.ANTILEECH_LOTION, ItemId.ANTILEECH_LOTION_30028),
    MAL: new BaseMix('MAL','Mixalot',  10, ItemId.MIXALOT, ItemId.MIXALOT_30030)
}

export const AllBaseMixes = Object.values(BaseMixes)