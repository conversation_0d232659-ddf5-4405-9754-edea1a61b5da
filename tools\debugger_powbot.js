let base = Module.findBaseAddress('liblibs.hal.system.osclient.so')



async function intercaptJava() {
    console.log('[*] Intercepting Java')
   
    // listAllClassLoaders()

    Java.enumerateLoadedClassesSync({
        onMatch: function(className) {
            if (className.includes('org.powbot')) {
                console.log('Powbot class found', className)
            }
        },
        onComplete: function() {
            console.log('Powbot class search completed')
        }
    })


    const pbLoader = await findPowbotClassLoader()
    console.log('Powbot ClassLoader found', pbLoader)


    // Java.ClassFactory.get(pbLoader).choose('org.powbot.client.OffsetsManager', {
    //     onMatch: function(instance) {
    //         console.log('OffsetsManager found', instance)
    //         console.log('OffsetsManager loaded? ', instance.loaded.value)
    //         console.log('OffsetsManager offsets? ', instance.offsets.value.size())

    //     },
    //     onComplete: function() {
    //         console.log('OffsetsManager search completed')
    //     }
    // })


    // Java.ClassFactory.get(pbLoader).choose('org.powbot.mobile.service.OffsetService', {
    //     onMatch: function(instance) {
    //         console.log('OffsetService found', instance)

    //         const offsetse = instance.getOffsets()
    //         console.log('OffsetService offsets? ', offsetse.size())
    //         console.log('Stringify trying')
    //         console.log('OffsetService offsets JSON: ', JSON.stringify(Java.use('java.util.HashMap').wrap(offsetse).toString()))

    //     },
    //     onComplete: function() {
    //         console.log('OffsetsManager search completed')
    //     }
    // })

    Java.ClassFactory.get(pbLoader).choose('org.powbot.mobile.BotManager', {
        onMatch: function(instance) {
            console.log('BotManager found', instance)

            console.log("Stealing url")
            console.log('BotManager offsets? ' + instance.getOffsetsUrl())

        },
        onComplete: function() {
            console.log('OffsetsManager search completed')
        }
    })

}

console.log('[*] Script loaded - starting hooks in 8 seconds...')

function onSend(message) {

    console.log('onCommand', message)
    if (message.payload == 'y') {
       
    } else if (message.payload == 'n') {
       
    } 

    recv('onCommand', onSend)
}


// Increase the delay to ensure the module is fully loaded
setTimeout(() => {
    console.log('[*] Starting hooks...')
    base = Module.findBaseAddress('liblibs.hal.system.osclient.so')
    intercaptJava()

    recv('onCommand', onSend)
    console.log('[*] Listening to actions...')
    console.log('[y] - Method has executed')
    console.log('[n] - Method has not executed')
    console.log('[r] - Restart')
    console.log('[s] - Show remaining symbols')
    console.log('[i] - Start intent')
    console.log('[c] - Call some function')
    console.log('[reload] - Reload the script')
}, 3500)

console.log('Recv registered')

//@prettier-ignore

function findPowbotClassLoader() {
    console.log('[*] Searching for Powbot ClassLoader');
    
    return new Promise((resolve, reject) => {
        Java.perform(() => {
            let powbotLoader = null;
            
            Java.enumerateClassLoaders({
                onMatch: function(loader) {
                    try {
                        // Check if parent is org.powbot.mobile.MobileClassLoader
                        const parent = loader.getParent();
                        if (parent) {
                            const parentClassName = parent.getClass().getName();
                            if (parentClassName === 'org.powbot.mobile.MobileClassLoader') {
                                console.log('[+] Found Powbot ClassLoader!');
                                console.log(`    Type: ${loader.getClass().getName()}`);
                                console.log(`    Parent: ${parentClassName}`);
                                console.log(`    Hash: ${loader.hashCode()}`);
                                powbotLoader = loader;
                            }
                        }
                    } catch(e) {
                        // Ignore errors when checking parents
                    }
                },
                onComplete: function() {
                    if (powbotLoader) {
                        console.log('[+] Powbot ClassLoader search completed successfully');
                        resolve(powbotLoader);
                    } else {
                        console.log('[-] Powbot ClassLoader not found');
                        reject(new Error('Powbot ClassLoader not found'));
                    }
                }
            });
        });
    });
}
