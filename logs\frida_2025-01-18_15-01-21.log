2025-01-18 15:01:21,948 - INFO - Connected to device: <PERSON><PERSON>(id="socket@**************:27042", name="**************:27042", type='remote')
2025-01-18 15:01:21,948 - INFO - Available processes:
2025-01-18 15:01:22,481 - INFO - Process(pid=15028, name="Gadget", parameters={})
2025-01-18 15:01:28,289 - INFO - Script loaded successfully
2025-01-18 15:01:33,635 - ERROR - Script error: TypeError: cannot read property 'value' of undefined |  TypeError: cannot read property 'value' of undefined
    at <anonymous> (/script1.js:2841)
    at <anonymous> (frida/node_modules/frida-java-bridge/lib/vm.js:12)
    at perform (frida/node_modules/frida-java-bridge/index.js:203)
    at click (/script1.js:2858)
    at handleConsoleCommand (/script1.js:14028)
    at handleCommand (/script1.js:50459)
    at <anonymous> (frida/runtime/message-dispatcher.js:56)
    at i (frida/runtime/message-dispatcher.js:42)
    at forEach (native)
    at c (frida/runtime/message-dispatcher.js:32)
    at o (frida/runtime/message-dispatcher.js:23)
2025-01-18 15:01:41,210 - ERROR - Script error: TypeError: cannot read property 'value' of undefined |  TypeError: cannot read property 'value' of undefined
    at <anonymous> (/script1.js:2841)
    at <anonymous> (frida/node_modules/frida-java-bridge/lib/vm.js:12)
    at perform (frida/node_modules/frida-java-bridge/index.js:203)
    at click (/script1.js:2858)
    at handleConsoleCommand (/script1.js:14028)
    at handleCommand (/script1.js:50459)
    at <anonymous> (frida/runtime/message-dispatcher.js:56)
    at i (frida/runtime/message-dispatcher.js:42)
    at forEach (native)
    at c (frida/runtime/message-dispatcher.js:32)
    at o (frida/runtime/message-dispatcher.js:23)
2025-01-18 15:03:56,158 - INFO - Reloading script...
2025-01-18 15:03:59,745 - ERROR - Script was destroyed - possible app crash
2025-01-18 15:03:59,745 - INFO - Attempting to reconnect...
