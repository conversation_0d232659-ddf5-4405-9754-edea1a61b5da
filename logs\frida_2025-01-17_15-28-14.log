2025-01-17 15:28:14,270 - INFO - Connected to device: <PERSON><PERSON>(id="socket@localhost:27042", name="localhost:27042", type='remote')
2025-01-17 15:28:14,270 - INFO - Available processes:
2025-01-17 15:28:14,527 - INFO - Process(pid=4667, name="Gadget", parameters={})
2025-01-17 15:28:15,998 - INFO - <PERSON><PERSON><PERSON> loaded successfully
2025-01-17 15:49:57,362 - INFO - Reloading script...
2025-01-17 15:50:00,992 - ERROR - <PERSON><PERSON><PERSON> was destroyed - possible app crash
2025-01-17 15:50:00,993 - INFO - Attempting to reconnect...
2025-01-17 15:50:02,993 - INFO - Reconnection attempt 1/3
2025-01-17 15:50:04,664 - INFO - <PERSON><PERSON><PERSON> loaded successfully
2025-01-17 15:50:04,666 - INFO - Successfully reconnected
2025-01-17 15:50:04,667 - INFO - Previous script unloaded.
2025-01-17 15:50:06,350 - INFO - <PERSON><PERSON><PERSON> loaded successfully
2025-01-17 15:50:06,353 - INFO - <PERSON><PERSON><PERSON> reloaded successfully.
2025-01-17 15:50:06,354 - INFO - Reloading script...
2025-01-17 15:50:06,871 - ERROR - Script error: Error: java.lang.IllegalArgumentException: View=android.view.View{a3d0111 V.ED..... ........ 0,0-0,0} not attached to window manager
2025-01-17 15:50:34,355 - ERROR - Script was destroyed - possible app crash
2025-01-17 15:50:34,355 - INFO - Attempting to reconnect...
2025-01-17 15:50:36,356 - INFO - Reconnection attempt 1/3
2025-01-17 15:50:37,824 - INFO - Script loaded successfully
2025-01-17 15:50:37,826 - INFO - Successfully reconnected
2025-01-17 15:50:37,826 - INFO - Previous script unloaded.
2025-01-17 15:50:39,205 - INFO - Script loaded successfully
2025-01-17 15:50:39,207 - INFO - Script reloaded successfully.
2025-01-17 15:50:39,207 - INFO - Reloading script...
2025-01-17 15:50:39,894 - ERROR - Script error: Error: java.lang.IllegalArgumentException: View=android.view.View{dd99829 V.ED..... ........ 0,0-0,0} not attached to window manager
2025-01-17 15:51:07,208 - ERROR - Script was destroyed - possible app crash
2025-01-17 15:51:07,208 - INFO - Attempting to reconnect...
2025-01-17 15:51:09,209 - INFO - Reconnection attempt 1/3
2025-01-17 15:51:10,620 - INFO - Script loaded successfully
2025-01-17 15:51:10,620 - INFO - Successfully reconnected
2025-01-17 15:51:10,621 - INFO - Previous script unloaded.
2025-01-17 15:51:12,151 - INFO - Script loaded successfully
2025-01-17 15:51:12,152 - INFO - Script reloaded successfully.
2025-01-17 15:52:38,578 - INFO - Reloading script...
2025-01-17 15:52:42,325 - ERROR - Script was destroyed - possible app crash
2025-01-17 15:52:42,325 - INFO - Attempting to reconnect...
2025-01-17 15:52:44,326 - INFO - Reconnection attempt 1/3
2025-01-17 15:52:46,233 - INFO - Script loaded successfully
2025-01-17 15:52:46,236 - INFO - Successfully reconnected
2025-01-17 15:52:46,236 - INFO - Previous script unloaded.
2025-01-17 15:52:48,146 - INFO - Script loaded successfully
2025-01-17 15:52:48,151 - INFO - Script reloaded successfully.
2025-01-17 15:52:48,151 - INFO - Reloading script...
2025-01-17 15:52:49,253 - ERROR - Script error: Error: java.lang.IllegalArgumentException: View=android.view.View{dd32dbe V.ED..... ........ 0,0-0,0} not attached to window manager
2025-01-17 15:53:16,154 - ERROR - Script was destroyed - possible app crash
2025-01-17 15:53:16,154 - INFO - Attempting to reconnect...
2025-01-17 15:53:18,155 - INFO - Reconnection attempt 1/3
2025-01-17 15:53:20,124 - INFO - Script loaded successfully
2025-01-17 15:53:20,126 - INFO - Successfully reconnected
2025-01-17 15:53:20,126 - INFO - Previous script unloaded.
2025-01-17 15:53:22,666 - INFO - Script loaded successfully
2025-01-17 15:53:22,668 - INFO - Script reloaded successfully.
2025-01-17 15:53:22,668 - INFO - Reloading script...
2025-01-17 15:53:22,855 - ERROR - Script error: Error: java.lang.IllegalArgumentException: View=android.view.View{76eb154 V.ED..... ........ 0,0-0,0} not attached to window manager
2025-01-17 15:53:23,493 - ERROR - Script was destroyed - possible app crash
2025-01-17 15:53:23,494 - INFO - Attempting to reconnect...
2025-01-17 15:53:25,494 - INFO - Reconnection attempt 1/3
2025-01-17 15:53:25,494 - ERROR - Reconnection attempt failed: the connection is closed
2025-01-17 15:53:27,495 - INFO - Reconnection attempt 2/3
2025-01-17 15:53:27,495 - ERROR - Script was destroyed - possible app crash
2025-01-17 15:53:27,495 - INFO - Attempting to reconnect...
2025-01-17 15:53:29,496 - INFO - Reconnection attempt 1/3
2025-01-17 15:53:29,496 - ERROR - Reconnection attempt failed: the connection is closed
2025-01-17 15:53:31,497 - INFO - Reconnection attempt 2/3
2025-01-17 15:53:31,497 - ERROR - Reconnection attempt failed: the connection is closed
2025-01-17 15:53:33,498 - INFO - Reconnection attempt 3/3
2025-01-17 15:53:33,498 - ERROR - Reconnection attempt failed: the connection is closed
2025-01-17 15:53:33,498 - ERROR - Failed to reconnect after maximum attempts
2025-01-17 15:53:33,498 - INFO - Previous script unloaded.
2025-01-17 15:53:33,498 - ERROR - Reconnection attempt failed: the connection is closed
2025-01-17 15:53:35,499 - INFO - Reconnection attempt 3/3
2025-01-17 15:53:35,508 - ERROR - Error loading script: the connection is closed
2025-01-17 15:53:35,509 - ERROR - Reconnection attempt failed: the connection is closed
2025-01-17 15:53:35,509 - ERROR - Failed to reconnect after maximum attempts
2025-01-17 15:53:35,509 - ERROR - Script was destroyed - possible app crash
2025-01-17 15:53:35,509 - INFO - Attempting to reconnect...
2025-01-17 15:53:37,512 - INFO - Reconnection attempt 1/3
2025-01-17 15:53:37,512 - ERROR - Reconnection attempt failed: the connection is closed
2025-01-17 15:53:39,513 - INFO - Reconnection attempt 2/3
2025-01-17 15:53:39,513 - ERROR - Reconnection attempt failed: the connection is closed
2025-01-17 15:53:41,514 - INFO - Reconnection attempt 3/3
2025-01-17 15:53:41,514 - ERROR - Reconnection attempt failed: the connection is closed
2025-01-17 15:53:41,514 - ERROR - Failed to reconnect after maximum attempts
2025-01-17 15:53:41,514 - ERROR - Script was destroyed - possible app crash
2025-01-17 15:53:41,514 - INFO - Attempting to reconnect...
2025-01-17 15:53:43,515 - INFO - Reconnection attempt 1/3
2025-01-17 15:53:43,516 - ERROR - Reconnection attempt failed: the connection is closed
2025-01-17 15:53:45,517 - INFO - Reconnection attempt 2/3
2025-01-17 15:53:45,517 - ERROR - Reconnection attempt failed: the connection is closed
2025-01-17 15:53:47,519 - INFO - Reconnection attempt 3/3
2025-01-17 15:53:47,520 - ERROR - Reconnection attempt failed: the connection is closed
2025-01-17 15:53:47,521 - ERROR - Failed to reconnect after maximum attempts
2025-01-17 15:53:47,521 - ERROR - Script was destroyed - possible app crash
2025-01-17 15:53:47,522 - INFO - Attempting to reconnect...
2025-01-17 15:53:49,522 - INFO - Reconnection attempt 1/3
2025-01-17 15:53:49,523 - ERROR - Reconnection attempt failed: the connection is closed
2025-01-17 15:53:51,524 - INFO - Reconnection attempt 2/3
2025-01-17 15:53:51,525 - ERROR - Reconnection attempt failed: the connection is closed
2025-01-17 15:53:53,527 - INFO - Reconnection attempt 3/3
2025-01-17 15:53:53,528 - ERROR - Reconnection attempt failed: the connection is closed
2025-01-17 15:53:53,529 - ERROR - Failed to reconnect after maximum attempts
2025-01-17 15:53:53,529 - ERROR - Script was destroyed - possible app crash
2025-01-17 15:53:53,530 - INFO - Attempting to reconnect...
2025-01-17 15:53:55,531 - INFO - Reconnection attempt 1/3
2025-01-17 15:53:55,532 - ERROR - Reconnection attempt failed: the connection is closed
2025-01-17 15:53:57,533 - INFO - Reconnection attempt 2/3
2025-01-17 15:53:57,534 - ERROR - Reconnection attempt failed: the connection is closed
2025-01-17 15:53:59,536 - INFO - Reconnection attempt 3/3
2025-01-17 15:53:59,536 - ERROR - Reconnection attempt failed: the connection is closed
2025-01-17 15:53:59,536 - ERROR - Failed to reconnect after maximum attempts
2025-01-17 15:56:00,941 - INFO - Reloading script...
2025-01-17 15:56:00,942 - ERROR - Error loading script: session is gone
2025-01-17 15:56:00,954 - INFO - Reloading script...
2025-01-17 15:56:01,249 - ERROR - Error loading script: session is gone
2025-01-17 15:56:02,312 - INFO - Reloading script...
2025-01-17 15:56:02,312 - ERROR - Error loading script: session is gone
2025-01-17 15:56:02,328 - INFO - Reloading script...
2025-01-17 15:56:02,583 - ERROR - Error loading script: session is gone
