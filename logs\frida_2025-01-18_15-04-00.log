2025-01-18 15:04:00,846 - INFO - Connected to device: Devi<PERSON>(id="socket@**************:27042", name="**************:27042", type='remote')
2025-01-18 15:04:00,846 - INFO - Available processes:
2025-01-18 15:04:01,377 - INFO - Process(pid=15302, name="Gadget", parameters={})
2025-01-18 15:04:05,694 - INFO - Script loaded successfully
2025-01-18 15:04:10,956 - ERROR - Script error: Error: sendInputEvent(): argument types do not match any of:
	.overload('android.view.InputEvent', 'java.lang.Object', 'boolean', 'android.view.InputQueue$FinishedInputEventCallback') |  Error: sendInputEvent(): argument types do not match any of:
	.overload('android.view.InputEvent', 'java.lang.Object', 'boolean', 'android.view.InputQueue$FinishedInputEventCallback')
    at Q (frida/node_modules/frida-java-bridge/lib/class-factory.js:626)
    at value (frida/node_modules/frida-java-bridge/lib/class-factory.js:1145)
    at e (frida/node_modules/frida-java-bridge/lib/class-factory.js:610)
    at <anonymous> (/script1.js:2836)
    at <anonymous> (frida/node_modules/frida-java-bridge/index.js:192)
2025-01-18 15:04:11,016 - ERROR - Script error: Error: sendInputEvent(): argument types do not match any of:
	.overload('android.view.InputEvent', 'java.lang.Object', 'boolean', 'android.view.InputQueue$FinishedInputEventCallback') |  Error: sendInputEvent(): argument types do not match any of:
	.overload('android.view.InputEvent', 'java.lang.Object', 'boolean', 'android.view.InputQueue$FinishedInputEventCallback')
    at Q (frida/node_modules/frida-java-bridge/lib/class-factory.js:626)
    at value (frida/node_modules/frida-java-bridge/lib/class-factory.js:1145)
    at e (frida/node_modules/frida-java-bridge/lib/class-factory.js:610)
    at <anonymous> (/script1.js:2844)
    at <anonymous> (frida/node_modules/frida-java-bridge/index.js:192)
2025-01-18 15:16:36,016 - ERROR - Script was destroyed - possible app crash
2025-01-18 15:16:36,017 - INFO - Attempting to reconnect...
2025-01-18 15:16:38,018 - INFO - Reconnection attempt 1/3
2025-01-18 15:16:38,018 - ERROR - Reconnection attempt failed: the connection is closed
2025-01-18 15:16:40,019 - INFO - Reconnection attempt 2/3
2025-01-18 15:16:40,019 - ERROR - Reconnection attempt failed: the connection is closed
2025-01-18 15:16:42,021 - INFO - Reconnection attempt 3/3
2025-01-18 15:16:42,021 - ERROR - Reconnection attempt failed: the connection is closed
2025-01-18 15:16:42,022 - ERROR - Failed to reconnect after maximum attempts
2025-01-18 15:29:27,132 - INFO - Reloading script...
2025-01-18 15:29:30,133 - ERROR - Error unloading script: script is destroyed
2025-01-18 15:29:30,434 - ERROR - Error loading script: session is gone
2025-01-18 15:29:30,435 - INFO - Reloading script...
2025-01-18 15:29:30,501 - ERROR - Error loading script: session is gone
