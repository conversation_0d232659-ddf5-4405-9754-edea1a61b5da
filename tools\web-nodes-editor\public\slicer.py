﻿"""
python slicer.py full_image_0.png --rows 3 --cols 4
Image slicer for extremely large images with control over rows and columns.
Allows specifying exact rows and columns or total number of tiles.
"""

import os
import sys
from PIL import Image

# Disable the DecompressionBomb warning/error
Image.MAX_IMAGE_PIXELS = None

def slice_by_grid(image_path, rows, cols):
    """
    Slice an image into a specific grid of rows and columns
    
    Args:
        image_path (str): Path to the image file
        rows (int): Number of rows to slice into
        cols (int): Number of columns to slice into
    
    Returns:
        list: List of paths to the sliced images
    """
    try:
        # Get image dimensions without loading full image
        with Image.open(image_path) as img:
            width, height = img.size
            print(f"Image dimensions: {width}x{height} pixels")
            print(f"Slicing into grid of {rows}x{cols} = {rows*cols} pieces")
            
            # Calculate slice dimensions
            slice_height = height // rows
            slice_width = width // cols
            
            # Create directory for slices
            base_name = os.path.splitext(os.path.basename(image_path))[0]
            output_dir = f"{base_name}_slices"
            os.makedirs(output_dir, exist_ok=True)
            
            # List to store the paths
            slice_paths = []
            
            # Create the slices
            for i in range(rows):
                for j in range(cols):
                    # Calculate coordinates
                    y_start = i * slice_height
                    y_end = min(y_start + slice_height, height) if i < rows-1 else height
                    x_start = j * slice_width
                    x_end = min(x_start + slice_width, width) if j < cols-1 else width
                    
                    print(f"Processing slice row {i+1}/{rows}, col {j+1}/{cols}: region ({x_start},{y_start}) to ({x_end},{y_end})")
                    
                    # Crop the image directly with PIL
                    with Image.open(image_path) as full_img:
                        # Crop region
                        region = full_img.crop((x_start, y_start, x_end, y_end))
                        
                        # Save the slice - using row/col naming convention
                        output_file = os.path.join(output_dir, f"{base_name}_r{i+1}c{j+1}.png")
                        region.save(output_file)
                        
                        # Add to paths list
                        slice_paths.append(output_file)
                        print(f"Saved {output_file}")
            
            print(f"Sliced image into {len(slice_paths)} pieces")
            return slice_paths
        
    except Exception as e:
        print(f"Error slicing image: {e}")
        return []

def slice(image_path, num_tiles=None, rows=None, cols=None):
    """
    Flexible slicing function that can work with either:
    - A total number of tiles (calculates closest grid)
    - Specific number of rows and columns
    
    Args:
        image_path (str): Path to the image file
        num_tiles (int, optional): Total number of tiles desired
        rows (int, optional): Number of rows to slice into
        cols (int, optional): Number of columns to slice into
    
    Returns:
        list: List of paths to the sliced images
    """
    # Case 1: Both rows and columns specified
    if rows is not None and cols is not None:
        return slice_by_grid(image_path, rows, cols)
    
    # Case 2: Only number of tiles specified
    elif num_tiles is not None:
        import math
        # Calculate closest grid size
        grid_size = int(math.sqrt(num_tiles))
        r = c = grid_size
        
        # If num_tiles is not a perfect square, adjust
        if r * c < num_tiles:
            c = c + 1
            
        print(f"Creating approximately {num_tiles} tiles by using a {r}x{c} grid")
        return slice_by_grid(image_path, r, c)
    
    # Case 3: Insufficient parameters
    else:
        print("Error: You must specify either num_tiles or both rows and cols")
        return []

if __name__ == "__main__":
    if len(sys.argv) < 3:
        print("Usage options:")
        print("1. Specify total number of tiles:")
        print("   python rows_cols_slicer.py <image_path> --tiles <number>")
        print("2. Specify rows and columns:")
        print("   python rows_cols_slicer.py <image_path> --rows <number> --cols <number>")
        print("\nExamples:")
        print("   python rows_cols_slicer.py full_image_0.png --tiles 6")
        print("   python rows_cols_slicer.py full_image_0.png --rows 2 --cols 3")
        sys.exit(1)
    
    image_path = sys.argv[1]
    
    # Parse command line arguments
    num_tiles = None
    rows = None
    cols = None
    
    i = 2
    while i < len(sys.argv):
        if sys.argv[i] == "--tiles" and i+1 < len(sys.argv):
            num_tiles = int(sys.argv[i+1])
            i += 2
        elif sys.argv[i] == "--rows" and i+1 < len(sys.argv):
            rows = int(sys.argv[i+1])
            i += 2
        elif sys.argv[i] == "--cols" and i+1 < len(sys.argv):
            cols = int(sys.argv[i+1])
            i += 2
        else:
            i += 1
    
    # Call the appropriate function
    slice(image_path, num_tiles, rows, cols)