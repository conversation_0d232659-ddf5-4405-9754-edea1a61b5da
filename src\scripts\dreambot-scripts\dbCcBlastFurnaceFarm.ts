import { DreambotScript } from '../../api/core/script/dreambotScript'
import { createState } from '../../api/core/script/state'
import { Bank } from '../../api/game/bank'
import { Inventory } from '../../api/game/inventory'
import { AccountProps } from '../../api/model/accountData'
import { ItemContainer } from '../../api/model/itemContainer'
import { TradePackage } from '../../api/model/tradePackage'
import { ResupplyMuleState } from '../../api/script-utils/mule/resupplyMuleStrategy'
import { Client } from '../../api/wrappers/client'
import { BotSettings } from '../../botSettings'
import { Bot } from '../../core/bot'
import { ItemId } from '../../data/itemId'
import { MuleReceiver } from '../muling/muleReceiver'

export class CcBlastFurnaceFarm extends DreambotScript {
    resupply = new ResupplyMuleState(TradePackage.forAccount(MuleReceiver.PACKAGE_RESUPPLY_2m), BotSettings.tradeWorldP2p)

    mainState = createState('Main', () => {
        if (Client.gameState < 30) {
            return
        }

        if(AccountProps.get('blastFurnanceReady') == 'true') {
            Bot.scriptHandler.startNext(this)
            return
        }
       

        if (!Bank.hasCache()) {
            Bank.openNearest()
            return
        }

        if(ItemContainer.allCombined().containsAllById(ItemId.ICE_GLOVES, ItemId.COAL_BAG_12019)) {
            AccountProps.set('blastFurnanceReady', 'true')
            Bot.scriptHandler.startNext(this)
            return
        }

        const container = Inventory.get().and(Bank.cache)
        if (container.countByIds(995) < 3_500_000) {
            this.mainState.setState(this.resupply)
            return
        }

        this.startDreambotScript('cCBlastFurnaceFarm')
    })

    onStart(): void {
        this.initWithState(this.mainState)
    }
}
