<?php
generateUpdateList();
function generateUpdateList() {
	$path = "./osrs-native";
	$files = getDirContents($path);
	foreach ($files as $file) {
		$fileName = substr($file, strlen($path));
		echo "$fileName<br>";
	}
}


function getDirContents($base_dir){
         $directories = array();
      foreach(scandir($base_dir) as $file) {
            if($file == '.' || $file == '..') continue;
            $dir = $base_dir."/".$file;
            if(is_dir($dir)) {
                
                $directories = array_merge($directories, getDirContents($dir));
            } else {
            	$directories [] = $dir;
            }
      }
      return $directories;
}

?>