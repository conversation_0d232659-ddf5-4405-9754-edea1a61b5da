<rule>
When working on the scripts
You need to always check for other scripts to see how similiar things are implemented. In 90% cases, we had similiar scenario that is already done.
</rule>


<rule>
Using bank and bank cache.
In many cases you do not need to check bank cache and then perform Withdraw.all method. Withdraw.all already checks if bank contains particular item.



Wrong example (redudant check for bank cache before Withdraw.all):
```
 if (!Bank.cache?.contains(ItemId.HARRALANDER, 28)) {
    // If we don't have enough herbs, go to GE to buy them
    this.setState(this.geState)
    return
}
if (!Withdraw.all(
    this.geState,
    Withdraw.id(ItemId.HARRALANDER, 28).ensureSpace()
)) {
    return
}
```

Good example (just use withdraw.all with fallback)
```
if (!Withdraw.all(
    this.geState,
    Withdraw.id(ItemId.HARRALANDER, 28).ensureSpace()
)) {
    return
}
``` 
</rule>

<rule>
Code style.
Try to avoid nested if's and non-readable code. Use guard clauses where possible, but not always. You need to decide when more readable is nested if, and where guard clauses are better.
</rule>