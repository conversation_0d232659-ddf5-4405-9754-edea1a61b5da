2025-01-18 16:29:26,850 - INFO - Connected to device: <PERSON><PERSON>(id="socket@**************:27042", name="**************:27042", type='remote')
2025-01-18 16:29:26,850 - INFO - Available processes:
2025-01-18 16:29:27,487 - INFO - Process(pid=15870, name="Gadget", parameters={})
2025-01-18 16:29:37,242 - INFO - <PERSON><PERSON><PERSON> loaded successfully
2025-01-18 16:29:41,365 - ERROR - Script error: Error: access violation accessing 0x948ceb5b50000004 |  Error: access violation accessing 0x948ceb5b50000004
    at <anonymous> (frida/runtime/core.js:145)
    at onEnter (/script1.js:30063)
2025-01-18 16:29:41,368 - ERROR - Script error: Error: access violation accessing 0x2f7665642f0003 |  Error: access violation accessing 0x2f7665642f0003
    at <anonymous> (frida/runtime/core.js:145)
    at onEnter (/script1.js:30063)
2025-01-18 16:29:41,369 - ERROR - Script error: Error: access violation accessing 0x2f7665642f0003 |  Error: access violation accessing 0x2f7665642f0003
    at <anonymous> (frida/runtime/core.js:145)
    at onEnter (/script1.js:30063)
2025-01-18 16:30:11,629 - ERROR - Script error: Error: access violation accessing 0x948ceb5b50000004 |  Error: access violation accessing 0x948ceb5b50000004
    at <anonymous> (frida/runtime/core.js:145)
    at onEnter (/script1.js:30063)
2025-01-18 16:30:11,630 - ERROR - Script error: Error: access violation accessing 0x2f7665642f0003 |  Error: access violation accessing 0x2f7665642f0003
    at <anonymous> (frida/runtime/core.js:145)
    at onEnter (/script1.js:30063)
2025-01-18 16:30:11,634 - ERROR - Script error: Error: access violation accessing 0x2f7665642f0003 |  Error: access violation accessing 0x2f7665642f0003
    at <anonymous> (frida/runtime/core.js:145)
    at onEnter (/script1.js:30063)
2025-01-18 16:35:02,718 - INFO - Reloading script...
2025-01-18 16:35:05,948 - ERROR - Script was destroyed - possible app crash
2025-01-18 16:35:05,948 - INFO - Attempting to reconnect...
