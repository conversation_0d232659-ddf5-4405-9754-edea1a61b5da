import { StatefulScript } from '../../api/core/script/statefulScript'
import { Skill } from '../../api/game/skill'
import { Varps } from '../../api/game/varps'
import { MixologyMain } from './states/mixologyMain'

export class Mixology extends StatefulScript {
    public static fruitsGained = 0
    public static lastFruitsCount = -1
    
     main: MixologyMain

    constructor() {
        super()
        this.loopInterval = 100
    }

    onStart(): void {
        this.main = new MixologyMain()
        this.initWithState(this.main)
        Mixology.fruitsGained = 0
        Mixology.lastFruitsCount = -1
    }

    onDraw(canvas: any, paint: any): void {
        this.drawText(" ")
        this.drawText(" ")
        this.drawText(" ")



        this.drawText(`Mixology (${this.currentState?.name})`)
        this.drawText('Runtime: ' + this.progressTracker.getTimeRunning())
        this.drawText('Herblore : ' + this.progressTracker.getExpGainedString(Skill.HERBLORE))
        this.drawText('Varps: ' + Varps.getVarbit(11339))
    }

    runtimeInfo(): string {
        // const farmingExp = `<th>${this.progressTracker.hourRatioString('Fruits', Mixology.fruitsGained)}</th>`
        // const farmingLevel = `<th>Farming: ${Skill.FARMING.getCurrentLevel()}</th>`
        const state = `<th>${this.currentState?.name}</th>`
        const herbloreExp = `<th>${this.progressTracker.getExpGainedString(Skill.HERBLORE)}</th>`
        const lyes = `<th>Lye: ${this.progressTracker.hourRatioString("Points", this.main.lyePoints)}</th>`

        return ` ${herbloreExp}${lyes}  ${state} `
    }
}
