import { Bot } from '../../../core/bot'
import { Tile } from '../../model/tile'
import { Time } from '../../utils/time'
import { Actor } from '../../wrappers/actor'
import { BotScript } from './botScript'
import { ExecutingScriptSubscriber, GameEvent } from './game-events/eventBus'
import { StatefulScript } from './statefulScript'

export abstract class State {
    onBackgroundAction(): void {}
    abstract onAction(): void

    public executing: boolean

    _name?: string | null
    _botScript: BotScript

    onGameMessage(username: string, message: string): void {}
    onCreate(): void {}
    onDraw(canvas: any, paint: any): void {}

    drawText(text: string) {
        this.getCurrentScript()?.drawText(text)
    }

    drawTile(tile: Tile, text: string = null, tileColor: string = '#06330056', textColor: string = '#15ff00ff') {
        this.getCurrentScript()?.drawTile(tile, text, tileColor, textColor)
    }

    drawActor(actor: Actor, text: string = null, textColor: string = '#15ff00ff') {
        this.getCurrentScript()?.drawActor(actor, text, textColor)
    }

    get name() {
        return this._name ?? this.constructor.name
    }

    set name(value: string) {
        this._name = value
    }

    public setState(container: State, calmly = false, interrupt = false, setAsDefault = false): void {
        const script = this.getCurrentScript<StatefulScript>()
        if (script) {
            script.currentState = container
            if (setAsDefault) {
                script.defaultState = container
            }
            if (interrupt) {
                Bot.scriptHandler.interruptCurrentInterval(true)
            }
            if (!calmly) {
                Time.sleep(1)
                script.currentState?.execute()
            }
        }
    }

    
    public execute(): void {
        this.executing = true
        try {
            this.onAction()
        } catch (e) {
            this.executing = false
            throw e
        }
    }

    public setDefaultState(): void {
        const script = this.getCurrentScript<StatefulScript>()
        if (script) {
            script.currentState = script.defaultState
        }
    }

    public getCurrentScript<T extends BotScript>(): T | null {
        return Bot.scriptHandler.currentScript as T
    }

    public getStatefulScript(): StatefulScript {
        return this.getCurrentScript<StatefulScript>()
    }

    public get script() {
        return this.getCurrentScript()
    }

    public startNextScript(): void {
        Bot.scriptHandler.startNext(this._botScript)
    }

    subscribe<T extends GameEvent>(eventType: new (...args: any[]) => T, $onEvent: (event: T) => void) {
        if (this._botScript == null) {
            console.error("Error: State doesn't have a bot script. Subscribe to events only in state's onCreate method.")
            return
        }

        Bot.eventBus.register(new ExecutingScriptSubscriber(eventType, this._botScript, $onEvent))
    }
}

export class BaseState extends State {
    onActionMethod: () => void

    constructor(onActionMethod: () => void) {
        super()
        this.onActionMethod = onActionMethod
    }

    onAction(): void {
        this.onActionMethod()
    }
}

export function createState(name: string, onAction: () => void) {
    const state = new BaseState(onAction)
    state.name = name
    return state
}
