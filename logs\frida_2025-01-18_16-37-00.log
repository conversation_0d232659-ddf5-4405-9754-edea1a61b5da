2025-01-18 16:37:00,328 - INFO - Connected to device: <PERSON><PERSON>(id="socket@**************:27042", name="**************:27042", type='remote')
2025-01-18 16:37:00,329 - INFO - Available processes:
2025-01-18 16:37:00,439 - INFO - Process(pid=36748, name="Gadget", parameters={})
2025-01-18 16:37:10,010 - INFO - <PERSON><PERSON><PERSON> loaded successfully
2025-01-18 16:38:17,117 - INFO - Reloading script...
2025-01-18 16:38:20,347 - ERROR - <PERSON><PERSON><PERSON> was destroyed - possible app crash
2025-01-18 16:38:20,347 - INFO - Attempting to reconnect...
2025-01-18 16:38:22,348 - INFO - Reconnection attempt 1/3
2025-01-18 16:38:31,575 - INFO - <PERSON><PERSON><PERSON> loaded successfully
2025-01-18 16:38:31,578 - INFO - Successfully reconnected
2025-01-18 16:38:31,579 - INFO - Previous script unloaded.
2025-01-18 16:38:40,547 - INFO - <PERSON><PERSON><PERSON> loaded successfully
2025-01-18 16:38:40,551 - INFO - <PERSON><PERSON><PERSON> reloaded successfully.
2025-01-18 16:38:40,551 - INFO - Reloading script...
2025-01-18 16:38:41,679 - ERROR - Script error: Error: java.lang.IllegalArgumentException: View=android.view.View{35a8c55 V.ED..... ........ 0,0-0,0} not attached to window manager |  Error: java.lang.IllegalArgumentException: View=android.view.View{35a8c55 V.ED..... ........ 0,0-0,0} not attached to window manager
    at <anonymous> (frida/node_modules/frida-java-bridge/lib/env.js:124)
    at value (frida/node_modules/frida-java-bridge/lib/class-factory.js:1237)
    at e (frida/node_modules/frida-java-bridge/lib/class-factory.js:643)
    at apply (native)
    at value (frida/node_modules/frida-java-bridge/lib/class-factory.js:1141)
    at e (frida/node_modules/frida-java-bridge/lib/class-factory.js:610)
    at <anonymous> (/script2.js:16944)
    at <anonymous> (frida/node_modules/frida-java-bridge/index.js:192)
2025-01-18 16:39:08,560 - ERROR - Script was destroyed - possible app crash
2025-01-18 16:39:08,561 - INFO - Attempting to reconnect...
2025-01-18 16:39:10,562 - INFO - Reconnection attempt 1/3
2025-01-18 16:39:20,608 - INFO - Script loaded successfully
2025-01-18 16:39:20,611 - INFO - Successfully reconnected
2025-01-18 16:39:20,611 - INFO - Previous script unloaded.
2025-01-18 16:39:31,626 - INFO - Script loaded successfully
2025-01-18 16:39:31,627 - INFO - Script reloaded successfully.
2025-01-18 16:40:45,588 - INFO - Reloading script...
2025-01-18 16:40:49,840 - ERROR - Script was destroyed - possible app crash
2025-01-18 16:40:49,841 - INFO - Attempting to reconnect...
2025-01-18 16:40:51,841 - INFO - Reconnection attempt 1/3
2025-01-18 16:41:02,844 - INFO - Script loaded successfully
2025-01-18 16:41:02,847 - INFO - Successfully reconnected
2025-01-18 16:41:02,848 - INFO - Previous script unloaded.
2025-01-18 16:41:13,747 - INFO - Script loaded successfully
2025-01-18 16:41:13,748 - INFO - Script reloaded successfully.
2025-01-18 16:41:13,748 - INFO - Reloading script...
2025-01-18 16:41:15,765 - ERROR - Script error: Error: java.lang.IllegalArgumentException: View=android.view.View{f7730b8 V.ED..... ......ID 0,0-0,0} not attached to window manager |  Error: java.lang.IllegalArgumentException: View=android.view.View{f7730b8 V.ED..... ......ID 0,0-0,0} not attached to window manager
    at <anonymous> (frida/node_modules/frida-java-bridge/lib/env.js:124)
    at value (frida/node_modules/frida-java-bridge/lib/class-factory.js:1237)
    at e (frida/node_modules/frida-java-bridge/lib/class-factory.js:643)
    at apply (native)
    at value (frida/node_modules/frida-java-bridge/lib/class-factory.js:1141)
    at e (frida/node_modules/frida-java-bridge/lib/class-factory.js:610)
    at <anonymous> (/script2.js:16944)
    at <anonymous> (frida/node_modules/frida-java-bridge/index.js:192)
2025-01-18 16:41:17,145 - ERROR - Script was destroyed - possible app crash
2025-01-18 16:41:17,145 - INFO - Attempting to reconnect...
2025-01-18 16:41:19,147 - INFO - Reconnection attempt 1/3
2025-01-18 16:41:31,722 - INFO - Script loaded successfully
2025-01-18 16:41:31,726 - INFO - Successfully reconnected
2025-01-18 16:41:31,726 - INFO - Previous script unloaded.
2025-01-18 16:41:44,190 - INFO - Script loaded successfully
2025-01-18 16:41:44,191 - INFO - Script reloaded successfully.
2025-01-18 16:42:10,656 - INFO - Reloading script...
